/**
 * SCRIPT RIMOZIONE CONSOLE.LOG
 * Rimuove automaticamente tutti i console.log dal codice
 */

const fs = require('fs');
const path = require('path');

class ConsoleLogRemover {
  constructor() {
    this.srcDir = path.join(__dirname, '../src');
    this.results = {
      filesProcessed: 0,
      logsRemoved: 0,
      errors: []
    };
  }

  log(message, color = 'reset') {
    const colors = {
      green: '\x1b[32m',
      red: '\x1b[31m',
      yellow: '\x1b[33m',
      blue: '\x1b[34m',
      reset: '\x1b[0m',
      bold: '\x1b[1m'
    };
    console.log(`${colors[color]}${message}${colors.reset}`);
  }

  getAllFiles(dir, fileList = []) {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        if (!file.startsWith('.') && file !== 'node_modules') {
          this.getAllFiles(filePath, fileList);
        }
      } else if (file.endsWith('.tsx') || file.endsWith('.ts') || file.endsWith('.js') || file.endsWith('.jsx')) {
        fileList.push(filePath);
      }
    });
    
    return fileList;
  }

  removeConsoleLogsFromFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      const originalLineCount = lines.length;
      let removedCount = 0;
      
      // Filtra le righe che contengono console.log
      const filteredLines = lines.filter(line => {
        const trimmedLine = line.trim();
        
        // Identifica righe con console.log (anche commentate)
        const hasConsoleLog = (
          trimmedLine.includes('console.log(') ||
          trimmedLine.includes('console.error(') ||
          trimmedLine.includes('console.warn(') ||
          trimmedLine.includes('console.info(') ||
          trimmedLine.includes('console.debug(')
        );
        
        if (hasConsoleLog) {
          // Non rimuovere se è in un commento di documentazione
          if (trimmedLine.startsWith('//') || trimmedLine.startsWith('*') || trimmedLine.startsWith('/*')) {
            return true; // Mantieni i commenti
          }
          
          removedCount++;
          return false; // Rimuovi la riga
        }
        
        return true; // Mantieni la riga
      });
      
      if (removedCount > 0) {
        const newContent = filteredLines.join('\n');
        fs.writeFileSync(filePath, newContent, 'utf8');
        
        const relativePath = filePath.replace(this.srcDir, '');
        this.log(`  ✅ ${relativePath}: rimossi ${removedCount} console.log`, 'green');
        this.results.logsRemoved += removedCount;
      }
      
      this.results.filesProcessed++;
      
    } catch (error) {
      const relativePath = filePath.replace(this.srcDir, '');
      this.results.errors.push({ file: relativePath, error: error.message });
      this.log(`  ❌ Errore in ${relativePath}: ${error.message}`, 'red');
    }
  }

  cleanupEmptyLines(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Rimuovi righe vuote multiple consecutive (max 2 righe vuote)
      const cleanedContent = content
        .replace(/\n\s*\n\s*\n/g, '\n\n') // Max 2 righe vuote consecutive
        .replace(/^\s*\n/, '') // Rimuovi righe vuote all'inizio
        .replace(/\n\s*$/, '\n'); // Assicura una sola riga vuota alla fine
      
      if (cleanedContent !== content) {
        fs.writeFileSync(filePath, cleanedContent, 'utf8');
      }
      
    } catch (error) {
      // Ignora errori di cleanup
    }
  }

  processAllFiles() {
    this.log('\n🧹 Rimozione Console.log in corso...', 'blue');
    this.log('='.repeat(50), 'blue');
    
    const allFiles = this.getAllFiles(this.srcDir);
    
    allFiles.forEach(filePath => {
      this.removeConsoleLogsFromFile(filePath);
      this.cleanupEmptyLines(filePath);
    });
  }

  printResults() {
    this.log('\n📊 RISULTATI PULIZIA CONSOLE.LOG', 'bold');
    this.log('='.repeat(50), 'blue');
    
    this.log(`File processati: ${this.results.filesProcessed}`, 'blue');
    this.log(`Console.log rimossi: ${this.results.logsRemoved}`, 'green');
    this.log(`Errori: ${this.results.errors.length}`, this.results.errors.length > 0 ? 'red' : 'green');
    
    if (this.results.errors.length > 0) {
      this.log('\n❌ ERRORI:', 'red');
      this.results.errors.forEach(error => {
        this.log(`  - ${error.file}: ${error.error}`, 'red');
      });
    }
    
    if (this.results.logsRemoved > 0) {
      this.log('\n✅ Pulizia completata con successo!', 'green');
      this.log('💡 Suggerimento: Verifica che il codice funzioni ancora correttamente', 'yellow');
    } else {
      this.log('\n✅ Nessun console.log trovato da rimuovere', 'green');
    }
  }

  async run() {
    this.log('\n🚀 Avvio Pulizia Console.log', 'bold');
    this.log('Questo script rimuoverà tutti i console.log dal codice sorgente', 'yellow');
    
    this.processAllFiles();
    this.printResults();
  }
}

// Esecuzione dello script
if (require.main === module) {
  const remover = new ConsoleLogRemover();
  remover.run().catch(error => {
    console.error('Errore fatale:', error);
    process.exit(1);
  });
}

module.exports = ConsoleLogRemover;
