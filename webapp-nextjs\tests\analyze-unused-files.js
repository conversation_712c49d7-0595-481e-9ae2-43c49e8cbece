/**
 * ANALISI FILE INUTILIZZATI E PULIZIA CODICE
 * Script per identificare file non utilizzati, console.log, e codice da pulire
 */

const fs = require('fs');
const path = require('path');

class CodeAnalyzer {
  constructor() {
    this.srcDir = path.join(__dirname, '../src');
    this.results = {
      unusedFiles: [],
      consoleLogsFound: [],
      todoComments: [],
      duplicateImports: [],
      largeFiles: [],
      emptyFiles: [],
      testFiles: []
    };
  }

  log(message, color = 'reset') {
    const colors = {
      green: '\x1b[32m',
      red: '\x1b[31m',
      yellow: '\x1b[33m',
      blue: '\x1b[34m',
      reset: '\x1b[0m',
      bold: '\x1b[1m'
    };
    console.log(`${colors[color]}${message}${colors.reset}`);
  }

  getAllFiles(dir, fileList = []) {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        if (!file.startsWith('.') && file !== 'node_modules') {
          this.getAllFiles(filePath, fileList);
        }
      } else if (file.endsWith('.tsx') || file.endsWith('.ts') || file.endsWith('.js') || file.endsWith('.jsx')) {
        fileList.push(filePath);
      }
    });
    
    return fileList;
  }

  analyzeFileUsage() {
    this.log('\n🔍 Analyzing file usage...', 'blue');
    
    const allFiles = this.getAllFiles(this.srcDir);
    const importedFiles = new Set();
    
    // Analizza tutti gli import
    allFiles.forEach(filePath => {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        const importRegex = /import.*from\s+['"`]([^'"`]+)['"`]/g;
        let match;
        
        while ((match = importRegex.exec(content)) !== null) {
          const importPath = match[1];
          
          // Risolvi path relativi
          if (importPath.startsWith('.')) {
            const resolvedPath = path.resolve(path.dirname(filePath), importPath);
            
            // Prova diverse estensioni
            const extensions = ['.tsx', '.ts', '.js', '.jsx'];
            for (const ext of extensions) {
              const fullPath = resolvedPath + ext;
              if (fs.existsSync(fullPath)) {
                importedFiles.add(fullPath);
                break;
              }
            }
            
            // Prova anche index files
            const indexPath = path.join(resolvedPath, 'index');
            for (const ext of extensions) {
              const fullPath = indexPath + ext;
              if (fs.existsSync(fullPath)) {
                importedFiles.add(fullPath);
                break;
              }
            }
          }
        }
      } catch (error) {
        this.log(`Error reading ${filePath}: ${error.message}`, 'red');
      }
    });

    // Trova file non utilizzati (escludi entry points)
    const entryPoints = [
      path.join(this.srcDir, 'app/layout.tsx'),
      path.join(this.srcDir, 'app/page.tsx'),
      path.join(this.srcDir, 'middleware.ts')
    ];

    allFiles.forEach(filePath => {
      const isEntryPoint = entryPoints.some(entry => filePath.includes(entry));
      const isPageFile = filePath.includes('/app/') && (filePath.endsWith('/page.tsx') || filePath.endsWith('/layout.tsx'));
      
      if (!importedFiles.has(filePath) && !isEntryPoint && !isPageFile) {
        this.results.unusedFiles.push(filePath.replace(this.srcDir, ''));
      }
    });
  }

  analyzeConsoleLogsAndTodos() {
    this.log('\n🔍 Analyzing console.logs and TODOs...', 'blue');
    
    const allFiles = this.getAllFiles(this.srcDir);
    
    allFiles.forEach(filePath => {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n');
        
        lines.forEach((line, index) => {
          const lineNumber = index + 1;
          const relativePath = filePath.replace(this.srcDir, '');
          
          // Console.log detection
          if (line.includes('console.log') || line.includes('console.error') || line.includes('console.warn')) {
            this.results.consoleLogsFound.push({
              file: relativePath,
              line: lineNumber,
              content: line.trim()
            });
          }
          
          // TODO/FIXME detection
          if (line.includes('TODO') || line.includes('FIXME') || line.includes('HACK')) {
            this.results.todoComments.push({
              file: relativePath,
              line: lineNumber,
              content: line.trim()
            });
          }
        });
      } catch (error) {
        this.log(`Error reading ${filePath}: ${error.message}`, 'red');
      }
    });
  }

  analyzeFileSize() {
    this.log('\n🔍 Analyzing file sizes...', 'blue');
    
    const allFiles = this.getAllFiles(this.srcDir);
    
    allFiles.forEach(filePath => {
      try {
        const stat = fs.statSync(filePath);
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n').length;
        const relativePath = filePath.replace(this.srcDir, '');
        
        // File troppo grandi (>500 righe)
        if (lines > 500) {
          this.results.largeFiles.push({
            file: relativePath,
            lines: lines,
            size: stat.size
          });
        }
        
        // File vuoti o quasi vuoti
        if (lines < 5 || content.trim().length < 50) {
          this.results.emptyFiles.push({
            file: relativePath,
            lines: lines,
            size: stat.size
          });
        }
      } catch (error) {
        this.log(`Error analyzing ${filePath}: ${error.message}`, 'red');
      }
    });
  }

  analyzeDuplicateImports() {
    this.log('\n🔍 Analyzing duplicate imports...', 'blue');
    
    const allFiles = this.getAllFiles(this.srcDir);
    
    allFiles.forEach(filePath => {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        const imports = [];
        const importRegex = /import.*from\s+['"`]([^'"`]+)['"`]/g;
        let match;
        
        while ((match = importRegex.exec(content)) !== null) {
          imports.push(match[1]);
        }
        
        // Trova duplicati
        const duplicates = imports.filter((item, index) => imports.indexOf(item) !== index);
        if (duplicates.length > 0) {
          this.results.duplicateImports.push({
            file: filePath.replace(this.srcDir, ''),
            duplicates: [...new Set(duplicates)]
          });
        }
      } catch (error) {
        this.log(`Error analyzing imports in ${filePath}: ${error.message}`, 'red');
      }
    });
  }

  findTestFiles() {
    this.log('\n🔍 Finding test files...', 'blue');
    
    const allFiles = this.getAllFiles(path.join(__dirname, '..'));
    
    allFiles.forEach(filePath => {
      const fileName = path.basename(filePath);
      const relativePath = filePath.replace(path.join(__dirname, '..'), '');
      
      if (fileName.includes('.test.') || fileName.includes('.spec.') || filePath.includes('/tests/') || filePath.includes('/__tests__/')) {
        this.results.testFiles.push(relativePath);
      }
    });
  }

  generateCleanupScript() {
    this.log('\n📝 Generating cleanup script...', 'blue');
    
    let script = '#!/bin/bash\n';
    script += '# Auto-generated cleanup script\n';
    script += '# Review before executing!\n\n';
    
    // Rimozione file inutilizzati
    if (this.results.unusedFiles.length > 0) {
      script += '# Remove unused files\n';
      this.results.unusedFiles.forEach(file => {
        script += `# rm "src${file}"\n`;
      });
      script += '\n';
    }
    
    // Rimozione console.log
    if (this.results.consoleLogsFound.length > 0) {
      script += '# Files with console.log to clean:\n';
      const filesWithConsole = [...new Set(this.results.consoleLogsFound.map(item => item.file))];
      filesWithConsole.forEach(file => {
        script += `# Clean console.log from src${file}\n`;
      });
      script += '\n';
    }
    
    fs.writeFileSync(path.join(__dirname, 'cleanup-script.sh'), script);
    this.log('Cleanup script generated: tests/cleanup-script.sh', 'green');
  }

  printResults() {
    this.log('\n📊 CODE ANALYSIS RESULTS', 'bold');
    this.log('='.repeat(50), 'blue');
    
    // File inutilizzati
    this.log(`\n📁 Unused Files (${this.results.unusedFiles.length}):`, 'yellow');
    if (this.results.unusedFiles.length > 0) {
      this.results.unusedFiles.forEach(file => {
        this.log(`  - src${file}`, 'red');
      });
    } else {
      this.log('  ✅ No unused files found', 'green');
    }
    
    // Console.log
    this.log(`\n🖥️  Console.log Found (${this.results.consoleLogsFound.length}):`, 'yellow');
    if (this.results.consoleLogsFound.length > 0) {
      this.results.consoleLogsFound.slice(0, 10).forEach(item => {
        this.log(`  - src${item.file}:${item.line} - ${item.content}`, 'red');
      });
      if (this.results.consoleLogsFound.length > 10) {
        this.log(`  ... and ${this.results.consoleLogsFound.length - 10} more`, 'yellow');
      }
    } else {
      this.log('  ✅ No console.log found', 'green');
    }
    
    // TODO comments
    this.log(`\n📝 TODO Comments (${this.results.todoComments.length}):`, 'yellow');
    if (this.results.todoComments.length > 0) {
      this.results.todoComments.slice(0, 5).forEach(item => {
        this.log(`  - src${item.file}:${item.line} - ${item.content}`, 'yellow');
      });
      if (this.results.todoComments.length > 5) {
        this.log(`  ... and ${this.results.todoComments.length - 5} more`, 'yellow');
      }
    } else {
      this.log('  ✅ No TODO comments found', 'green');
    }
    
    // File grandi
    this.log(`\n📏 Large Files (${this.results.largeFiles.length}):`, 'yellow');
    if (this.results.largeFiles.length > 0) {
      this.results.largeFiles.forEach(item => {
        this.log(`  - src${item.file} (${item.lines} lines)`, 'yellow');
      });
    } else {
      this.log('  ✅ No overly large files found', 'green');
    }
    
    // File vuoti
    this.log(`\n📄 Empty/Small Files (${this.results.emptyFiles.length}):`, 'yellow');
    if (this.results.emptyFiles.length > 0) {
      this.results.emptyFiles.forEach(item => {
        this.log(`  - src${item.file} (${item.lines} lines)`, 'red');
      });
    } else {
      this.log('  ✅ No empty files found', 'green');
    }
    
    // Import duplicati
    this.log(`\n🔄 Duplicate Imports (${this.results.duplicateImports.length}):`, 'yellow');
    if (this.results.duplicateImports.length > 0) {
      this.results.duplicateImports.forEach(item => {
        this.log(`  - src${item.file}: ${item.duplicates.join(', ')}`, 'yellow');
      });
    } else {
      this.log('  ✅ No duplicate imports found', 'green');
    }
  }

  async runAnalysis() {
    this.log('\n🚀 Starting Code Analysis', 'bold');
    this.log('='.repeat(50), 'blue');
    
    this.analyzeFileUsage();
    this.analyzeConsoleLogsAndTodos();
    this.analyzeFileSize();
    this.analyzeDuplicateImports();
    this.findTestFiles();
    
    this.printResults();
    this.generateCleanupScript();
    
    this.log('\n✅ Analysis complete!', 'green');
  }
}

// Esecuzione dell'analisi
if (require.main === module) {
  const analyzer = new CodeAnalyzer();
  analyzer.runAnalysis().catch(error => {
    console.error('Fatal error running analysis:', error);
    process.exit(1);
  });
}

module.exports = CodeAnalyzer;
