'use client'

import { useState, useEffect, useMemo } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow} from '@/components/ui/table'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue} from '@/components/ui/select'
import {
  Popover,
  PopoverContent,
  PopoverTrigger} from '@/components/ui/popover'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Filter,
  ChevronDown,
  ChevronUp,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  X,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from 'lucide-react'
import { cn } from '@/lib/utils'

export interface ColumnDef {
  field: string

interface FilterableTableProps {
  data: any[]

interface SortConfig {
  key: string | null

interface FilterConfig {
  [key: string]: {
    type: 'text' | 'select' | 'number'

export default function FilterableTable({
  data = [],
  columns = [],
  loading = false,
  emptyMessage = 'Nessun dato disponibile',
  onFilteredDataChange,
  renderRow,
  className,
  pagination = true,
  defaultRowsPerPage = 25
}: FilterableTableProps) {
  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: null, direction: null })
  const [filters, setFilters] = useState<FilterConfig>({})
  const [openFilters, setOpenFilters] = useState<{ [key: string]: boolean }>({})
  const [currentPage, setCurrentPage] = useState(0)
  const [rowsPerPage, setRowsPerPage] = useState(defaultRowsPerPage)

  // Get unique values for select filters
  const getUniqueValues = (field: string) => {
    return [...new Set(data.map(item => item[field]).filter(Boolean))].sort()

  // Apply filters and sorting
  const filteredAndSortedData = useMemo(() => {
    let filtered = [...data]

    // Apply filters
    Object.entries(filters).forEach(([field, filterConfig]) => {
      if (!filterConfig.value || 
          (Array.isArray(filterConfig.value) && filterConfig.value.length === 0) ||
          (typeof filterConfig.value === 'string' && filterConfig.value.trim() === '')) {
        return

      filtered = filtered.filter(item => {
        const itemValue = item[field]
        
        if (filterConfig.type === 'select') {
          const selectedValues = Array.isArray(filterConfig.value) ? filterConfig.value : [filterConfig.value]
          return selectedValues.includes(itemValue)

        if (filterConfig.type === 'text') {
          const searchValue = (filterConfig.value as string).toLowerCase()
          const cellValue = String(itemValue || '').toLowerCase()
          
          if (filterConfig.operator === 'equals') {
            return cellValue === searchValue

          return cellValue.includes(searchValue)

        if (filterConfig.type === 'number') {
          const numValue = parseFloat(itemValue)
          const filterValue = parseFloat(filterConfig.value as string)
          
          if (isNaN(numValue) || isNaN(filterValue)) return false
          
          switch (filterConfig.operator) {
            case 'equals': return numValue === filterValue
            case 'gt': return numValue > filterValue
            case 'lt': return numValue < filterValue
            case 'gte': return numValue >= filterValue
            case 'lte': return numValue <= filterValue

        return true
      })
    })

    // Apply sorting
    if (sortConfig.key && sortConfig.direction) {
      filtered.sort((a, b) => {
        const aValue = a[sortConfig.key!]
        const bValue = b[sortConfig.key!]
        
        // Handle null/undefined values
        if (aValue == null && bValue == null) return 0
        if (aValue == null) return sortConfig.direction === 'asc' ? -1 : 1
        if (bValue == null) return sortConfig.direction === 'asc' ? 1 : -1
        
        // Determine if values are numbers
        const aNum = parseFloat(aValue)
        const bNum = parseFloat(bValue)
        const isNumeric = !isNaN(aNum) && !isNaN(bNum)
        
        let comparison = 0
        if (isNumeric) {
          comparison = aNum - bNum
        } else {
          comparison = String(aValue).localeCompare(String(bValue))

        return sortConfig.direction === 'asc' ? comparison : -comparison
      })

    return filtered
  }, [data, filters, sortConfig])

  // Calculate paginated data
  const paginatedData = useMemo(() => {
    if (!pagination) return filteredAndSortedData

    const startIndex = currentPage * rowsPerPage
    const endIndex = startIndex + rowsPerPage
    return filteredAndSortedData.slice(startIndex, endIndex)
  }, [filteredAndSortedData, currentPage, rowsPerPage, pagination])

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(0)
  }, [filters])

  // Calculate pagination info
  const totalPages = Math.ceil(filteredAndSortedData.length / rowsPerPage)
  const startRow = currentPage * rowsPerPage + 1
  const endRow = Math.min((currentPage + 1) * rowsPerPage, filteredAndSortedData.length)

  // Notify parent of filtered data changes
  useEffect(() => {
    if (onFilteredDataChange) {
      onFilteredDataChange(filteredAndSortedData)

  }, [filteredAndSortedData, onFilteredDataChange])

  const handleSort = (field: string) => {
    const column = columns.find(col => col.field === field)
    if (column?.disableSort) return

    setSortConfig(prev => {
      if (prev.key === field) {
        if (prev.direction === 'asc') return { key: field, direction: 'desc' }
        if (prev.direction === 'desc') return { key: null, direction: null }

      return { key: field, direction: 'asc' }
    })

  const updateFilter = (field: string, filterConfig: Partial<FilterConfig[string]>) => {
    setFilters(prev => ({
      ...prev,
      [field]: { ...prev[field], ...filterConfig }
    }))

  const clearFilter = (field: string) => {
    setFilters(prev => {
      const newFilters = { ...prev }
      delete newFilters[field]
      return newFilters
    })

  const clearAllFilters = () => {
    setFilters({})

  const getSortIcon = (field: string) => {
    if (sortConfig.key !== field) return <ArrowUpDown className="h-3 w-3" />
    if (sortConfig.direction === 'asc') return <ArrowUp className="h-3 w-3" />
    if (sortConfig.direction === 'desc') return <ArrowDown className="h-3 w-3" />
    return <ArrowUpDown className="h-3 w-3" />

  const hasActiveFilters = Object.keys(filters).length > 0

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center">Caricamento...</div>
        </CardContent>
      </Card>
    )

  return (
    <div className={className}>
      {/* Active filters display */}
      {hasActiveFilters && (
        <div className="mb-4 flex flex-wrap gap-2 items-center">
          <span className="text-sm text-muted-foreground">Filtri attivi:</span>
          {Object.entries(filters).map(([field, filterConfig]) => {
            const column = columns.find(col => col.field === field)
            if (!column) return null
            
            const displayValue = Array.isArray(filterConfig.value) 
              ? filterConfig.value.join(', ')
              : String(filterConfig.value)
            
            return (
              <Badge key={field} variant="secondary" className="gap-1">
                {column.headerName}: {displayValue}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 hover:bg-transparent"
                  onClick={() => clearFilter(field)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            )
          })}
          <Button
            variant="outline"
            size="sm"
            onClick={clearAllFilters}
            className="h-6 px-2 text-xs"
          >
            Pulisci tutti
          </Button>
        </div>
      )}

      {/* Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow className="bg-mariner-50 hover:bg-mariner-50">
                {columns.map((column) => (
                  <TableHead
                    key={column.field}
                    className={cn(
                      "font-semibold text-mariner-900 border-b border-mariner-200",
                      column.align === 'center' && "text-center",
                      column.align === 'right' && "text-right"
                    )}
                    style={{ width: column.width, ...column.headerStyle }}
                  >
                    {column.renderHeader ? (
                      column.renderHeader()
                    ) : (
                      <div className="relative group">
                        <div className="flex items-center justify-between w-full">
                          <span className="truncate">{column.headerName}</span>

                          {/* Compact icons container - only visible on hover */}
                          <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                            {/* Sort button */}
                            {!column.disableSort && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-4 w-4 p-0 hover:bg-mariner-100"
                                onClick={() => handleSort(column.field)}
                              >
                                {getSortIcon(column.field)}
                              </Button>
                            )}

                            {/* Filter button */}
                            {!column.disableFilter && (
                              <Popover
                                open={openFilters[column.field]}
                                onOpenChange={(open) => setOpenFilters(prev => ({ ...prev, [column.field]: open }))}
                              >
                                <PopoverTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className={cn(
                                      "h-4 w-4 p-0 hover:bg-mariner-100",
                                      filters[column.field] && "text-mariner-600 opacity-100"
                                    )}
                                  >
                                    <Filter className="h-2.5 w-2.5" />
                                  </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-64" align="start">
                                  <FilterContent
                                    column={column}
                                    data={data}
                                    currentFilter={filters[column.field]}
                                    onFilterChange={(filterConfig) => updateFilter(column.field, filterConfig)}
                                    onClearFilter={() => clearFilter(column.field)}
                                    getUniqueValues={() => getUniqueValues(column.field)}
                                  />
                                </PopoverContent>
                              </Popover>
                            )}
                          </div>
                        </div>

                        {/* Active filter indicator */}
                        {filters[column.field] && (
                          <div className="absolute -top-1 -right-1 h-2 w-2 bg-mariner-600 rounded-full"></div>
                        )}
                      </div>
                    )}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedData.length > 0 ? (
                paginatedData.map((row, index) => (
                  renderRow ? (
                    renderRow(row, currentPage * rowsPerPage + index)
                  ) : (
                    <TableRow
                      key={index}
                      className="hover:bg-mariner-50 border-b border-mariner-100"
                    >
                      {columns.map((column) => (
                        <TableCell
                          key={column.field}
                          className={cn(
                            "py-2 px-4",
                            column.align === 'center' && "text-center",
                            column.align === 'right' && "text-right"
                          )}
                          style={column.cellStyle}
                        >
                          {column.renderCell ? column.renderCell(row) : row[column.field]}
                        </TableCell>
                      ))}
                    </TableRow>
                  )
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="text-center py-8 text-muted-foreground">
                    {emptyMessage}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Pagination Controls */}
      {pagination && filteredAndSortedData.length > 0 && (
        <div className="flex items-center justify-between mt-4">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">
              Righe per pagina:
            </span>
            <Select
              value={rowsPerPage.toString()}
              onValueChange={(value) => {
                setRowsPerPage(Number(value))
                setCurrentPage(0)
              }}
            >
              <SelectTrigger className="w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
                <SelectItem value={filteredAndSortedData.length.toString()}>Tutto</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">
              {filteredAndSortedData.length > 0 ? `${startRow}-${endRow} di ${filteredAndSortedData.length}` : '0 di 0'}
            </span>

            <div className="flex items-center space-x-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(0)}
                disabled={currentPage === 0}
                className="h-8 w-8 p-0"
              >
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(0, prev - 1))}
                disabled={currentPage === 0}
                className="h-8 w-8 p-0"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(totalPages - 1, prev + 1))}
                disabled={currentPage >= totalPages - 1}
                className="h-8 w-8 p-0"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(totalPages - 1)}
                disabled={currentPage >= totalPages - 1}
                className="h-8 w-8 p-0"
              >
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )

// Filter content component
interface FilterContentProps {
  column: ColumnDef

function FilterContent({
  column,
  currentFilter,
  onFilterChange,
  onClearFilter,
  getUniqueValues
}: FilterContentProps) {
  const [localValue, setLocalValue] = useState(currentFilter?.value || '')
  const [operator, setOperator] = useState(currentFilter?.operator || 'contains')

  const uniqueValues = getUniqueValues()
  const isSelectType = column.dataType !== 'number' && uniqueValues.length <= 20
  const isNumberType = column.dataType === 'number'

  const applyFilter = () => {
    if (isSelectType) {
      onFilterChange({
        type: 'select')
    } else if (isNumberType) {
      onFilterChange({
        type: 'number')
    } else {
      onFilterChange({
        type: 'text')

  return (
    <div className="space-y-3">
      <div className="font-medium text-sm">Filtra {column.headerName}</div>
      
      {isSelectType ? (
        <div className="space-y-2 max-h-48 overflow-y-auto">
          {uniqueValues.map(value => (
            <div key={value} className="flex items-center space-x-2">
              <Checkbox
                id={`,ilter-${value}`}
                checked={Array.isArray(localValue) ? localValue.includes(value) : localValue === value}
                onCheckedChange={(checked) => {
                  if (Array.isArray(localValue)) {
                    setLocalValue(checked 
                      ? [...localValue, value]
                      : localValue.filter(v => v !== value)
                    )
                  } else {
                    setLocalValue(checked ? [value] : [])

                }}
              />
              <label htmlFor={`,ilter-${value}`} className="text-sm">
                {value}
              </label>
            </div>
          ))}
        </div>
      ) : (
        <div className="space-y-2">
          {isNumberType && (
            <Select value={operator} onValueChange={setOperator}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="equals">Uguale a</SelectItem>
                <SelectItem value="gt">Maggiore di</SelectItem>
                <SelectItem value="lt">Minore di</SelectItem>
                <SelectItem value="gte">Maggiore o uguale</SelectItem>
                <SelectItem value="lte">Minore o uguale</SelectItem>
              </SelectContent>
            </Select>
          )}
          
          {!isNumberType && (
            <Select value={operator} onValueChange={setOperator}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="contains">Contiene</SelectItem>
                <SelectItem value="equals">Uguale a</SelectItem>
              </SelectContent>
            </Select>
          )}
          
          <Input
            placeholder={`,erca ${column.headerName.toLowerCase()}...`}
            value={localValue as string}
            onChange={(e) => setLocalValue(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && applyFilter()}
          />
        </div>
      )}
      
      <div className="flex gap-2">
        <Button size="sm" onClick={applyFilter}>
          Applica
        </Button>
        <Button size="sm" variant="outline" onClick={onClearFilter}>
          Pulisci
        </Button>
      </div>
    </div>
  )
