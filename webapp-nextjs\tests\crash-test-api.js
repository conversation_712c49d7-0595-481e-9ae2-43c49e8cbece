/**
 * CRASH TEST SUITE - API Backend Testing
 * Test completo delle funzionalità API del sistema CMS
 */

const axios = require('axios');

// Configurazione base
const BASE_URL = 'http://localhost:8001';
const TEST_TIMEOUT = 10000;

// Colori per output console
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

class CrashTestAPI {
  constructor() {
    this.results = {
      total: 0,
      passed: 0,
      failed: 0,
      errors: []
    };
    this.authToken = null;
  }

  log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
  }

  async runTest(testName, testFunction) {
    this.results.total++;
    try {
      this.log(`\n🧪 Testing: ${testName}`, 'blue');
      await testFunction();
      this.results.passed++;
      this.log(`✅ PASSED: ${testName}`, 'green');
    } catch (error) {
      this.results.failed++;
      this.results.errors.push({ test: testName, error: error.message });
      this.log(`❌ FAILED: ${testName}`, 'red');
      this.log(`   Error: ${error.message}`, 'red');
    }
  }

  async testServerConnection() {
    const response = await axios.get(`${BASE_URL}/health`, { timeout: TEST_TIMEOUT });
    if (response.status !== 200) {
      throw new Error(`Server not responding correctly. Status: ${response.status}`);
    }
  }

  async testAuthentication() {
    // Test login con credenziali di test
    const loginData = {
      username: 'admin',
      password: 'admin123'
    };
    
    const response = await axios.post(`${BASE_URL}/login`, loginData, { timeout: TEST_TIMEOUT });
    
    if (response.status !== 200 || !response.data.access_token) {
      throw new Error('Authentication failed - no token received');
    }
    
    this.authToken = response.data.access_token;
  }

  async testCantieriAPI() {
    if (!this.authToken) throw new Error('No auth token available');
    
    const headers = { Authorization: `Bearer ${this.authToken}` };
    
    // Test GET cantieri
    const response = await axios.get(`${BASE_URL}/cantieri`, { headers, timeout: TEST_TIMEOUT });
    
    if (response.status !== 200 || !Array.isArray(response.data)) {
      throw new Error('Cantieri API not returning valid data');
    }
  }

  async testCaviAPI() {
    if (!this.authToken) throw new Error('No auth token available');
    
    const headers = { Authorization: `Bearer ${this.authToken}` };
    
    // Test GET cavi
    const response = await axios.get(`${BASE_URL}/cavi`, { headers, timeout: TEST_TIMEOUT });
    
    if (response.status !== 200 || !Array.isArray(response.data)) {
      throw new Error('Cavi API not returning valid data');
    }
  }

  async testBobineAPI() {
    if (!this.authToken) throw new Error('No auth token available');
    
    const headers = { Authorization: `Bearer ${this.authToken}` };
    
    // Test GET bobine
    const response = await axios.get(`${BASE_URL}/bobine`, { headers, timeout: TEST_TIMEOUT });
    
    if (response.status !== 200 || !Array.isArray(response.data)) {
      throw new Error('Bobine API not returning valid data');
    }
  }

  async testComandeAPI() {
    if (!this.authToken) throw new Error('No auth token available');
    
    const headers = { Authorization: `Bearer ${this.authToken}` };
    
    // Test GET comande
    const response = await axios.get(`${BASE_URL}/comande`, { headers, timeout: TEST_TIMEOUT });
    
    if (response.status !== 200 || !Array.isArray(response.data)) {
      throw new Error('Comande API not returning valid data');
    }
  }

  async testReportsAPI() {
    if (!this.authToken) throw new Error('No auth token available');
    
    const headers = { Authorization: `Bearer ${this.authToken}` };
    
    // Test GET reports/boq
    const response = await axios.get(`${BASE_URL}/reports/boq`, { headers, timeout: TEST_TIMEOUT });
    
    if (response.status !== 200) {
      throw new Error('Reports API not responding correctly');
    }
  }

  async testDatabaseConnection() {
    if (!this.authToken) throw new Error('No auth token available');
    
    const headers = { Authorization: `Bearer ${this.authToken}` };
    
    // Test database status
    const response = await axios.get(`${BASE_URL}/admin/database/status`, { headers, timeout: TEST_TIMEOUT });
    
    if (response.status !== 200 || !response.data.connected) {
      throw new Error('Database connection failed');
    }
  }

  async runAllTests() {
    this.log('\n🚀 Starting CMS API Crash Test Suite', 'bold');
    this.log('=' * 50, 'blue');

    // Test di connessione base
    await this.runTest('Server Connection', () => this.testServerConnection());
    
    // Test autenticazione
    await this.runTest('Authentication', () => this.testAuthentication());
    
    // Test API principali
    await this.runTest('Cantieri API', () => this.testCantieriAPI());
    await this.runTest('Cavi API', () => this.testCaviAPI());
    await this.runTest('Bobine API', () => this.testBobineAPI());
    await this.runTest('Comande API', () => this.testComandeAPI());
    await this.runTest('Reports API', () => this.testReportsAPI());
    
    // Test database
    await this.runTest('Database Connection', () => this.testDatabaseConnection());

    // Risultati finali
    this.printResults();
  }

  printResults() {
    this.log('\n📊 TEST RESULTS', 'bold');
    this.log('=' * 50, 'blue');
    this.log(`Total Tests: ${this.results.total}`, 'blue');
    this.log(`Passed: ${this.results.passed}`, 'green');
    this.log(`Failed: ${this.results.failed}`, 'red');
    
    if (this.results.errors.length > 0) {
      this.log('\n❌ ERRORS:', 'red');
      this.results.errors.forEach(error => {
        this.log(`  - ${error.test}: ${error.error}`, 'red');
      });
    }
    
    const successRate = ((this.results.passed / this.results.total) * 100).toFixed(1);
    this.log(`\nSuccess Rate: ${successRate}%`, successRate > 80 ? 'green' : 'red');
    
    if (this.results.failed === 0) {
      this.log('\n🎉 ALL TESTS PASSED!', 'green');
    } else {
      this.log('\n⚠️  SOME TESTS FAILED - CHECK ERRORS ABOVE', 'yellow');
    }
  }
}

// Esecuzione dei test
if (require.main === module) {
  const tester = new CrashTestAPI();
  tester.runAllTests().catch(error => {
    console.error('Fatal error running tests:', error);
    process.exit(1);
  });
}

module.exports = CrashTestAPI;
