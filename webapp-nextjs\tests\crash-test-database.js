/**
 * TEST FUNZIONALITÀ DATABASE
 * Verifica connessioni, operazioni CRUD, integrità dati e performance
 */

const axios = require('axios');

class DatabaseTester {
  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001';
    this.token = null;
    this.results = {
      connection: { passed: 0, failed: 0, tests: [] },
      crud: { passed: 0, failed: 0, tests: [] },
      integrity: { passed: 0, failed: 0, tests: [] },
      performance: { passed: 0, failed: 0, tests: [] }
    };
  }

  log(message, color = 'reset') {
    const colors = {
      green: '\x1b[32m',
      red: '\x1b[31m',
      yellow: '\x1b[33m',
      blue: '\x1b[34m',
      reset: '\x1b[0m',
      bold: '\x1b[1m'
    };
    console.log(`${colors[color]}${message}${colors.reset}`);
  }

  async authenticate() {
    try {
      this.log('\n🔐 Autenticazione...', 'blue');

      // Usa FormData per OAuth2PasswordRequestForm
      const formData = new FormData();
      formData.append('username', 'admin');
      formData.append('password', 'admin');

      const response = await axios.post(`${this.baseUrl}/api/auth/login`, formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        timeout: 10000
      });

      if (response.data.access_token) {
        this.token = response.data.access_token;
        this.log('✅ Autenticazione riuscita', 'green');
        return true;
      }

      throw new Error('Token non ricevuto');
    } catch (error) {
      this.log(`❌ Errore autenticazione: ${error.message}`, 'red');
      return false;
    }
  }

  async testDatabaseConnection() {
    this.log('\n🔌 Test Connessione Database', 'blue');
    this.log('='.repeat(50), 'blue');

    const tests = [
      {
        name: 'Connessione base',
        test: async () => {
          const response = await axios.get(`${this.baseUrl}/api/health`, { timeout: 5000 });
          return response.status === 200;
        }
      },
      {
        name: 'Connessione autenticata',
        test: async () => {
          const response = await axios.get(`${this.baseUrl}/api/cantieri`, {
            headers: { Authorization: `Bearer ${this.token}` },
            timeout: 5000
          });
          return response.status === 200;
        }
      },
      {
        name: 'Pool connessioni',
        test: async () => {
          const promises = Array(5).fill().map(() => 
            axios.get(`${this.baseUrl}/api/cantieri`, {
              headers: { Authorization: `Bearer ${this.token}` },
              timeout: 5000
            })
          );
          const results = await Promise.all(promises);
          return results.every(r => r.status === 200);
        }
      }
    ];

    for (const test of tests) {
      try {
        const startTime = Date.now();
        const passed = await test.test();
        const duration = Date.now() - startTime;
        
        if (passed) {
          this.log(`  ✅ ${test.name} (${duration}ms)`, 'green');
          this.results.connection.passed++;
        } else {
          this.log(`  ❌ ${test.name} - Test fallito`, 'red');
          this.results.connection.failed++;
        }
        
        this.results.connection.tests.push({
          name: test.name,
          passed,
          duration
        });
      } catch (error) {
        this.log(`  ❌ ${test.name} - ${error.message}`, 'red');
        this.results.connection.failed++;
        this.results.connection.tests.push({
          name: test.name,
          passed: false,
          error: error.message
        });
      }
    }
  }

  async testCRUDOperations() {
    this.log('\n📝 Test Operazioni CRUD', 'blue');
    this.log('='.repeat(50), 'blue');

    const tests = [
      {
        name: 'CREATE - Nuovo cantiere',
        test: async () => {
          const response = await axios.post(`${this.baseUrl}/api/cantieri`, {
            commessa: `TEST_${Date.now()}`,
            descrizione: 'Cantiere di test',
            password_cantiere: 'test123'
          }, {
            headers: { Authorization: `Bearer ${this.token}` },
            timeout: 10000
          });
          this.testCantiereId = response.data.id_cantiere;
          return response.status === 201;
        }
      },
      {
        name: 'READ - Lista cantieri',
        test: async () => {
          const response = await axios.get(`${this.baseUrl}/api/cantieri`, {
            headers: { Authorization: `Bearer ${this.token}` },
            timeout: 5000
          });
          return response.status === 200 && Array.isArray(response.data);
        }
      },
      {
        name: 'READ - Dettaglio cantiere',
        test: async () => {
          if (!this.testCantiereId) return false;
          const response = await axios.get(`${this.baseUrl}/api/cantieri/${this.testCantiereId}`, {
            headers: { Authorization: `Bearer ${this.token}` },
            timeout: 5000
          });
          return response.status === 200;
        }
      },
      {
        name: 'UPDATE - Modifica cantiere',
        test: async () => {
          if (!this.testCantiereId) return false;
          const response = await axios.put(`${this.baseUrl}/api/cantieri/${this.testCantiereId}`, {
            descrizione: 'Cantiere di test modificato'
          }, {
            headers: { Authorization: `Bearer ${this.token}` },
            timeout: 10000
          });
          return response.status === 200;
        }
      },
      {
        name: 'DELETE - Elimina cantiere',
        test: async () => {
          if (!this.testCantiereId) return false;
          const response = await axios.delete(`${this.baseUrl}/api/cantieri/${this.testCantiereId}`, {
            headers: { Authorization: `Bearer ${this.token}` },
            timeout: 10000
          });
          return response.status === 200;
        }
      }
    ];

    for (const test of tests) {
      try {
        const startTime = Date.now();
        const passed = await test.test();
        const duration = Date.now() - startTime;
        
        if (passed) {
          this.log(`  ✅ ${test.name} (${duration}ms)`, 'green');
          this.results.crud.passed++;
        } else {
          this.log(`  ❌ ${test.name} - Test fallito`, 'red');
          this.results.crud.failed++;
        }
        
        this.results.crud.tests.push({
          name: test.name,
          passed,
          duration
        });
      } catch (error) {
        this.log(`  ❌ ${test.name} - ${error.message}`, 'red');
        this.results.crud.failed++;
        this.results.crud.tests.push({
          name: test.name,
          passed: false,
          error: error.message
        });
      }
    }
  }

  async testDataIntegrity() {
    this.log('\n🔒 Test Integrità Dati', 'blue');
    this.log('='.repeat(50), 'blue');

    const tests = [
      {
        name: 'Validazione input - Cantiere senza commessa',
        test: async () => {
          try {
            await axios.post(`${this.baseUrl}/api/cantieri`, {
              descrizione: 'Test senza commessa'
            }, {
              headers: { Authorization: `Bearer ${this.token}` },
              timeout: 5000
            });
            return false; // Dovrebbe fallire
          } catch (error) {
            return error.response?.status === 422; // Validation error
          }
        }
      },
      {
        name: 'Constraint database - Commessa duplicata',
        test: async () => {
          try {
            const commessa = `DUPLICATE_${Date.now()}`;
            
            // Prima creazione
            await axios.post(`${this.baseUrl}/api/cantieri`, {
              commessa,
              descrizione: 'Test duplicato 1',
              password_cantiere: 'test123'
            }, {
              headers: { Authorization: `Bearer ${this.token}` },
              timeout: 5000
            });
            
            // Seconda creazione (dovrebbe fallire)
            await axios.post(`${this.baseUrl}/api/cantieri`, {
              commessa,
              descrizione: 'Test duplicato 2',
              password_cantiere: 'test123'
            }, {
              headers: { Authorization: `Bearer ${this.token}` },
              timeout: 5000
            });
            
            return false; // Dovrebbe fallire
          } catch (error) {
            return error.response?.status >= 400; // Conflict error
          }
        }
      },
      {
        name: 'Relazioni foreign key',
        test: async () => {
          try {
            // Tenta di creare un cavo con cantiere inesistente
            await axios.post(`${this.baseUrl}/api/cavi`, {
              id_cantiere: 99999,
              sistema: 'TEST',
              utility: 'TEST',
              tipologia: 'TEST',
              sezione: 'TEST'
            }, {
              headers: { Authorization: `Bearer ${this.token}` },
              timeout: 5000
            });
            return false; // Dovrebbe fallire
          } catch (error) {
            return error.response?.status >= 400; // Foreign key error
          }
        }
      }
    ];

    for (const test of tests) {
      try {
        const startTime = Date.now();
        const passed = await test.test();
        const duration = Date.now() - startTime;
        
        if (passed) {
          this.log(`  ✅ ${test.name} (${duration}ms)`, 'green');
          this.results.integrity.passed++;
        } else {
          this.log(`  ❌ ${test.name} - Test fallito`, 'red');
          this.results.integrity.failed++;
        }
        
        this.results.integrity.tests.push({
          name: test.name,
          passed,
          duration
        });
      } catch (error) {
        this.log(`  ❌ ${test.name} - ${error.message}`, 'red');
        this.results.integrity.failed++;
        this.results.integrity.tests.push({
          name: test.name,
          passed: false,
          error: error.message
        });
      }
    }
  }

  async testPerformance() {
    this.log('\n⚡ Test Performance', 'blue');
    this.log('='.repeat(50), 'blue');

    const tests = [
      {
        name: 'Query semplice (<500ms)',
        test: async () => {
          const startTime = Date.now();
          await axios.get(`${this.baseUrl}/api/cantieri`, {
            headers: { Authorization: `Bearer ${this.token}` },
            timeout: 5000
          });
          const duration = Date.now() - startTime;
          return duration < 500;
        }
      },
      {
        name: 'Query complessa cavi (<2000ms)',
        test: async () => {
          const startTime = Date.now();
          await axios.get(`${this.baseUrl}/api/cavi?limit=100`, {
            headers: { Authorization: `Bearer ${this.token}` },
            timeout: 10000
          });
          const duration = Date.now() - startTime;
          return duration < 2000;
        }
      },
      {
        name: 'Carico concorrente (5 richieste)',
        test: async () => {
          const startTime = Date.now();
          const promises = Array(5).fill().map(() => 
            axios.get(`${this.baseUrl}/api/cantieri`, {
              headers: { Authorization: `Bearer ${this.token}` },
              timeout: 5000
            })
          );
          await Promise.all(promises);
          const duration = Date.now() - startTime;
          return duration < 3000;
        }
      }
    ];

    for (const test of tests) {
      try {
        const startTime = Date.now();
        const passed = await test.test();
        const duration = Date.now() - startTime;
        
        if (passed) {
          this.log(`  ✅ ${test.name} (${duration}ms)`, 'green');
          this.results.performance.passed++;
        } else {
          this.log(`  ❌ ${test.name} - Test fallito (${duration}ms)`, 'red');
          this.results.performance.failed++;
        }
        
        this.results.performance.tests.push({
          name: test.name,
          passed,
          duration
        });
      } catch (error) {
        this.log(`  ❌ ${test.name} - ${error.message}`, 'red');
        this.results.performance.failed++;
        this.results.performance.tests.push({
          name: test.name,
          passed: false,
          error: error.message
        });
      }
    }
  }

  printSummary() {
    this.log('\n📊 RIEPILOGO TEST DATABASE', 'bold');
    this.log('='.repeat(60), 'blue');
    
    const categories = [
      { name: 'Connessione', results: this.results.connection },
      { name: 'CRUD', results: this.results.crud },
      { name: 'Integrità', results: this.results.integrity },
      { name: 'Performance', results: this.results.performance }
    ];
    
    let totalPassed = 0;
    let totalFailed = 0;
    
    categories.forEach(category => {
      const { passed, failed } = category.results;
      const total = passed + failed;
      const percentage = total > 0 ? Math.round((passed / total) * 100) : 0;
      
      this.log(`\n${category.name}: ${passed}/${total} (${percentage}%)`, 
        percentage >= 80 ? 'green' : percentage >= 60 ? 'yellow' : 'red');
      
      totalPassed += passed;
      totalFailed += failed;
    });
    
    const grandTotal = totalPassed + totalFailed;
    const overallPercentage = grandTotal > 0 ? Math.round((totalPassed / grandTotal) * 100) : 0;
    
    this.log(`\n🎯 TOTALE: ${totalPassed}/${grandTotal} (${overallPercentage}%)`, 
      overallPercentage >= 80 ? 'green' : overallPercentage >= 60 ? 'yellow' : 'red');
    
    if (overallPercentage >= 80) {
      this.log('\n✅ Database funziona correttamente!', 'green');
    } else if (overallPercentage >= 60) {
      this.log('\n⚠️  Database ha alcuni problemi da risolvere', 'yellow');
    } else {
      this.log('\n❌ Database ha problemi critici', 'red');
    }
  }

  async run() {
    this.log('\n🚀 Test Funzionalità Database', 'bold');
    this.log('Verifica connessioni, CRUD, integrità e performance', 'blue');
    
    if (!(await this.authenticate())) {
      this.log('\n❌ Impossibile procedere senza autenticazione', 'red');
      return;
    }
    
    await this.testDatabaseConnection();
    await this.testCRUDOperations();
    await this.testDataIntegrity();
    await this.testPerformance();
    
    this.printSummary();
  }
}

// Esecuzione dei test
if (require.main === module) {
  const tester = new DatabaseTester();
  tester.run().catch(error => {
    console.error('Errore fatale:', error);
    process.exit(1);
  });
}

module.exports = DatabaseTester;
