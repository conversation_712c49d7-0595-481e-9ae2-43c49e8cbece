/**
 * Palette di colori morbidi per il sistema CMS
 * Evita il rosso fuoco e usa tonalità più professionali
 */

export const SOFT_COLORS = {
  // Stati di successo - Verde morbido
  },

  // Stati di warning - Giallo ambra/senape
  },

  // Stati di attenzione - Arancione tenue
  },

  // Stati di errore - Rosso morbido (non fuoco)
  },

  // Stati informativi - Blu morbido
  },

  // Stati neutri - Grigio
  },

  // Stati di progresso - Indaco

/**
 * Colori specifici per stati bobine
 */
export const BOBINA_COLORS = {
  DISPONIBILE: SOFT_COLORS.SUCCESS

/**
 * Colori specifici per stati cavi
 */
export const CAVO_COLORS = {
  DA_INSTALLARE: SOFT_COLORS.NEUTRAL

/**
 * Colori specifici per stati comande
 */
export const COMANDA_COLORS = {
  ATTIVA: SOFT_COLORS.SUCCESS

/**
 * Funzioni helper per ottenere classi CSS
 */
export const getSoftColorClasses = (colorType: keyof typeof SOFT_COLORS) => {
  const color = SOFT_COLORS[colorType]
  return {
    badge: ,${color.bg} ${color.text} ${color.border}`,
    button:  ,${color.bg} ${color.text} ${color.border} ${color.hover}`,
    alert:  ,${color.bg} ${color.text} ${color.border}`,
    text: color.text ${color.text} ${color.border}`,
    button:  ,${color.bg} ${color.text} ${color.border} ${color.hover}`,
    alert:  ,${color.bg} ${color.text} ${color.border}`,
    text: color.text,
    bg: color.bg,
    border: color.border,
    hover: color.hover,
    hex: color.hex

export const getBobinaColorClasses = (stato: string) => {
  const normalizedStato = stato?.toUpperCase() as keyof typeof BOBINA_COLORS
  const color = BOBINA_COLORS[normalizedStato] || BOBINA_COLORS.ERRORE
  
  return {
    badge: ,${color.bg} ${color.text} ${color.border}`,
    button:  ,${color.bg} ${color.text} ${color.border} ${color.hover}`,
    alert:  ,${color.bg} ${color.text} ${color.border}`,
    text: color.text ${color.text} ${color.border}`,
    button:  ,${color.bg} ${color.text} ${color.border} ${color.hover}`,
    alert:  ,${color.bg} ${color.text} ${color.border}`,
    text: color.text,
    bg: color.bg,
    border: color.border,
    hover: color.hover,
    hex: color.hex

export const getCavoColorClasses = (stato: string) => {
  const normalizedStato = stato?.toUpperCase().replace(/\s+/g, '_') as keyof typeof CAVO_COLORS
  const color = CAVO_COLORS[normalizedStato] || CAVO_COLORS.ERRORE
  
  return {
    badge:  ,${color.bg} ${color.text} ${color.border}`,
    button:  ,${color.bg} ${color.text} ${color.border} ${color.hover}`,
    alert:  ,${color.bg} ${color.text} ${color.border}`,
    text: color.text ${color.text} ${color.border}`,
    button:  ,${color.bg} ${color.text} ${color.border} ${color.hover}`,
    alert:  ,${color.bg} ${color.text} ${color.border}`,
    text: color.text,
    bg: color.bg,
    border: color.border,
    hover: color.hover,
    hex: color.hex

export const getComandaColorClasses = (stato: string) => {
  const normalizedStato = stato?.toUpperCase().replace(/\s+/g, '_') as keyof typeof COMANDA_COLORS
  const color = COMANDA_COLORS[normalizedStato] || COMANDA_COLORS.ERRORE
  
  return {
    badge:  ,${color.bg} ${color.text} ${color.border}`,
    button:  ,${color.bg} ${color.text} ${color.border} ${color.hover}`,
    alert:  ,${color.bg} ${color.text} ${color.border}`,
    text: color.text ${color.text} ${color.border}`,
    button:  ,${color.bg} ${color.text} ${color.border} ${color.hover}`,
    alert:  ,${color.bg} ${color.text} ${color.border}`,
    text: color.text,
    bg: color.bg,
    border: color.border,
    hover: color.hover,
    hex: color.hex

/**
 * Colori per percentuali di progresso
 */
export const getProgressColor = (percentage: number) => {
  if (percentage >= 90) return SOFT_COLORS.SUCCESS
  if (percentage >= 70) return SOFT_COLORS.PROGRESS
  if (percentage >= 50) return SOFT_COLORS.INFO
  if (percentage >= 30) return SOFT_COLORS.WARNING
  return SOFT_COLORS.ATTENTION

/**
 * Colori per priorità
 */
export const PRIORITY_COLORS = {
  ALTA: SOFT_COLORS.ERROR

export const getPriorityColorClasses = (priority: string) => {
  const normalizedPriority = priority?.toUpperCase() as keyof typeof PRIORITY_COLORS
  const color = PRIORITY_COLORS[normalizedPriority] || PRIORITY_COLORS.NORMALE
  
  return {
    badge: ,${color.bg} ${color.text} ${color.border}`,
    button:  ,${color.bg} ${color.text} ${color.border} ${color.hover}`,
    alert:  ,${color.bg} ${color.text} ${color.border}`,
    text: color.text ${color.text} ${color.border}`,
    button:  ,${color.bg} ${color.text} ${color.border} ${color.hover}`,
    alert:  ,${color.bg} ${color.text} ${color.border}`,
    text: color.text,
    bg: color.bg,
    border: color.border,
    hover: color.hover,
    hex: color.hex

