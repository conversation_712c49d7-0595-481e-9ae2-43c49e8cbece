'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/AuthContext'
import { Cable, User, Building2, AlertCircle, Loader2, Shield } from 'lucide-react'
import { validateUsername, checkRateLimit } from '@/utils/securityValidation'
import { useSecurityMonitoring } from '@/hooks/useSecurityMonitoring'

export default function LoginPage() {
  const [loginType, setLoginType] = useState<'user' | 'cantiere'>('user')
  const [formData, setFormData] = useState({
    username: '')
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})

  const { login, loginCantiere } = useAuth()
  const router = useRouter()
  const { logLoginAttempt, logSuspiciousActivity, getThreatLevel } = useSecurityMonitoring()

  // Validazione sicura del form
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {}

    if (loginType === 'user') {
      // Valida username
      const usernameValidation = validateUsername(formData.username)
      if (!usernameValidation.isValid) {
        errors.username = usernameValidation.error!

      // Valida password
      if (!formData.password) {
        errors.password = 'Password è obbligatoria'
      } else if (formData.password.length < 3) {
        errors.password = 'Password troppo corta'

    } else {
      // Valida codice cantiere
      if (!formData.codice_cantiere.trim()) {
        errors.codice_cantiere = 'Codice cantiere è obbligatorio'
      } else if (formData.codice_cantiere.length < 3) {
        errors.codice_cantiere = 'Codice cantiere troppo corto'

      // Valida password cantiere
      if (!formData.password_cantiere) {
        errors.password_cantiere = 'Password cantiere è obbligatoria'

    setValidationErrors(errors)
    return Object.keys(errors).length === 0

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setValidationErrors({})

    // Rate limiting per prevenire brute force
    const rateLimitKey = loginType === 'user' ? formData.username : formData.codice_cantiere
    if (!checkRateLimit(`login-${rateLimitKey}`, 5, 300000)) { // 5 tentativi per 5 minuti
      setError('Troppi tentativi di login. Riprova tra 5 minuti.')
      logSuspiciousActivity('rate_limit_exceeded', { loginType, identifier: rateLimitKey })
      return

    // Validazione form
    if (!validateForm()) {
      return

    // Controlla livello di minaccia
    const threatLevel = getThreatLevel()
    if (threatLevel === 'critical') {
      setError('Sistema temporaneamente non disponibile per motivi di sicurezza.')
      logSuspiciousActivity('login_blocked_threat_level', { threatLevel })
      return

    setIsLoading(true)

    try {
      if (loginType === 'user') {
        const userData = await login(formData.username, formData.password)

        // Log tentativo di login
        logLoginAttempt(formData.username, true, { ruolo: userData?.ruolo })

        // Reindirizza in base al ruolo
        if (userData?.ruolo === 'owner') {
          router.push('/admin')
        } else if (userData?.ruolo === 'user') {
          router.push('/cantieri')
        } else if (userData?.ruolo === 'cantieri_user') {
          router.push('/cavi')
        } else {
          router.push('/cantieri')

      } else {
        await loginCantiere(formData.codice_cantiere, formData.password_cantiere)

        // Log tentativo di login cantiere
        logLoginAttempt(formData.codice_cantiere, true, { type: 'cantiere' })

        router.push('/cavi')

    } catch (error: any) {
      const identifier = loginType === 'user' ? formData.username : formData.codice_cantiere

      // Log tentativo fallito
      logLoginAttempt(identifier, false, {
        error: error.response?.data?.detail || error.message,
        loginType
      })

      setError(error.response?.data?.detail || 'Credenziali non valide')
    } finally {
      setIsLoading(false)

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    setError('')

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-slate-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        
        {/* Header */}
        <div className="text-center space-y-2">
          <div className="flex justify-center">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center">
              <Cable className="w-8 h-8 text-white" />
            </div>
          </div>
          <h1 className="text-2xl font-bold text-slate-900">CABLYS</h1>
          <p className="text-slate-600">Cable Installation Advance System</p>
        </div>

        {/* Login Type Selector */}
        <div className="flex gap-2">
          <Button
            variant={loginType === 'user' ? 'default' : 'outline'}
            className="flex-1"
            onClick={() => setLoginType('user')}
          >
            <User className="w-4 h-4 mr-2" />
            Utente
          </Button>
          <Button
            variant={loginType === 'cantiere' ? 'default' : 'outline'}
            className="flex-1"
            onClick={() => setLoginType('cantiere')}
          >
            <Building2 className="w-4 h-4 mr-2" />
            Cantiere
          </Button>
        </div>

        {/* Login Form */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {loginType === 'user' ? (
                <>
                  <User className="w-5 h-5 text-blue-600" />
                  Login Utente
                </>
              ) : (
                <>
                  <Building2 className="w-5 h-5 text-green-600" />
                  Login Cantiere
                </>
              )}
            </CardTitle>
            <CardDescription>
              {loginType === 'user' 
                ? 'Accedi con le tue credenziali utente'
                : 'Accedi con il codice cantiere'

            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              
              {loginType === 'user' ? (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="username">Username</Label>
                    <Input
                      id="username"
                      type="text"
                      placeholder="Inserisci username"
                      value={formData.username}
                      onChange={(e) => handleInputChange('username', e.target.value)}
                      required
                      disabled={isLoading}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="password">Password</Label>
                    <Input
                      id="password"
                      type="password"
                      placeholder="Inserisci password"
                      value={formData.password}
                      onChange={(e) => handleInputChange('password', e.target.value)}
                      required
                      disabled={isLoading}
                    />
                  </div>
                </>
              ) : (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="codice_cantiere">Codice Cantiere</Label>
                    <Input
                      id="codice_cantiere"
                      type="text"
                      placeholder="Inserisci codice cantiere"
                      value={formData.codice_cantiere}
                      onChange={(e) => handleInputChange('codice_cantiere', e.target.value)}
                      required
                      disabled={isLoading}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="password_cantiere">Password Cantiere</Label>
                    <Input
                      id="password_cantiere"
                      type="password"
                      placeholder="Inserisci password cantiere"
                      value={formData.password_cantiere}
                      onChange={(e) => handleInputChange('password_cantiere', e.target.value)}
                      required
                      disabled={isLoading}
                    />
                  </div>
                </>
              )}

              {error && (
                <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <AlertCircle className="w-4 h-4 text-red-500" />
                  <span className="text-sm text-red-700">{error}</span>
                </div>
              )}

              <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Accesso in corso...
                  </>
                ) : (
                  'Accedi'
                )}
              </Button>

              {/* Link Password Dimenticata */}
              {loginType === 'user' && (
                <div className="text-center">
                  <Button
                    type="button"
                    variant="link"
                    className="text-sm text-blue-600 hover:text-blue-800"
                    onClick={() => router.push('/forgot-password')}
                  >
                    Password dimenticata?
                  </Button>
                </div>
              )}
            </form>
          </CardContent>
        </Card>

        {/* Info */}
        <div className="text-center space-y-2">
          <div className="flex justify-center gap-2">
            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              Next.js 15
            </Badge>
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              PWA Ready
            </Badge>
          </div>
          <p className="text-xs text-slate-500">
            Sistema di gestione cavi di nuova generazione
          </p>
        </div>

      </div>
    </div>
  )
