/**
 * MASTER TEST RUNNER
 * Esegue tutti i crash test e analisi del sistema CMS
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Colori per output console
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
  cyan: '\x1b[36m'
};

class MasterTestRunner {
  constructor() {
    this.results = {
      codeAnalysis: { status: 'pending', errors: [] },
      apiTests: { status: 'pending', errors: [] },
      frontendTests: { status: 'pending', errors: [] },
      systemHealth: { status: 'pending', errors: [] }
    };
    this.startTime = Date.now();
  }

  log(message, color = 'reset') {
    const timestamp = new Date().toLocaleTimeString();
    console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
  }

  async runCommand(command, args = [], options = {}) {
    return new Promise((resolve, reject) => {
      const child = spawn(command, args, {
        stdio: 'pipe',
        shell: true,
        ...options
      });

      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data) => {
        stdout += data.toString();
        if (options.showOutput) {
          process.stdout.write(data);
        }
      });

      child.stderr.on('data', (data) => {
        stderr += data.toString();
        if (options.showOutput) {
          process.stderr.write(data);
        }
      });

      child.on('close', (code) => {
        if (code === 0) {
          resolve({ stdout, stderr, code });
        } else {
          reject(new Error(`Command failed with code ${code}: ${stderr}`));
        }
      });

      child.on('error', (error) => {
        reject(error);
      });
    });
  }

  async checkSystemHealth() {
    this.log('\n🏥 Checking System Health...', 'cyan');
    
    try {
      // Verifica che Node.js sia installato
      await this.runCommand('node', ['--version']);
      this.log('✅ Node.js is available', 'green');
      
      // Verifica che npm sia installato
      await this.runCommand('npm', ['--version']);
      this.log('✅ npm is available', 'green');
      
      // Verifica che le dipendenze siano installate
      const nodeModulesPath = path.join(__dirname, '../node_modules');
      if (fs.existsSync(nodeModulesPath)) {
        this.log('✅ node_modules exists', 'green');
      } else {
        throw new Error('node_modules not found - run npm install');
      }
      
      // Verifica file di configurazione
      const configFiles = [
        '../package.json',
        '../next.config.ts',
        '../tailwind.config.js'
      ];
      
      for (const file of configFiles) {
        const filePath = path.join(__dirname, file);
        if (fs.existsSync(filePath)) {
          this.log(`✅ ${file} exists`, 'green');
        } else {
          throw new Error(`Configuration file missing: ${file}`);
        }
      }
      
      this.results.systemHealth.status = 'passed';
      this.log('✅ System health check passed', 'green');
      
    } catch (error) {
      this.results.systemHealth.status = 'failed';
      this.results.systemHealth.errors.push(error.message);
      this.log(`❌ System health check failed: ${error.message}`, 'red');
    }
  }

  async runCodeAnalysis() {
    this.log('\n🔍 Running Code Analysis...', 'cyan');
    
    try {
      const result = await this.runCommand('node', [
        path.join(__dirname, 'analyze-unused-files.js')
      ], { showOutput: true });
      
      this.results.codeAnalysis.status = 'passed';
      this.log('✅ Code analysis completed', 'green');
      
    } catch (error) {
      this.results.codeAnalysis.status = 'failed';
      this.results.codeAnalysis.errors.push(error.message);
      this.log(`❌ Code analysis failed: ${error.message}`, 'red');
    }
  }

  async runAPITests() {
    this.log('\n🌐 Running API Tests...', 'cyan');
    
    try {
      // Prima verifica se il backend è in esecuzione
      const axios = require('axios');
      try {
        await axios.get('http://localhost:8001/health', { timeout: 5000 });
        this.log('✅ Backend server is running', 'green');
      } catch (error) {
        throw new Error('Backend server not running on port 8001. Start it first with: python main.py');
      }
      
      const result = await this.runCommand('node', [
        path.join(__dirname, 'crash-test-api.js')
      ], { showOutput: true });
      
      this.results.apiTests.status = 'passed';
      this.log('✅ API tests completed', 'green');
      
    } catch (error) {
      this.results.apiTests.status = 'failed';
      this.results.apiTests.errors.push(error.message);
      this.log(`❌ API tests failed: ${error.message}`, 'red');
    }
  }

  async runFrontendTests() {
    this.log('\n🖥️  Running Frontend Tests...', 'cyan');
    
    try {
      // Prima verifica se il frontend è in esecuzione
      const axios = require('axios');
      try {
        await axios.get('http://localhost:3000', { timeout: 5000 });
        this.log('✅ Frontend server is running', 'green');
      } catch (error) {
        throw new Error('Frontend server not running on port 3000. Start it first with: npm run dev');
      }
      
      // Verifica se puppeteer è installato
      try {
        require('puppeteer');
      } catch (error) {
        this.log('⚠️  Puppeteer not installed. Installing...', 'yellow');
        await this.runCommand('npm', ['install', 'puppeteer'], { showOutput: true });
      }
      
      const result = await this.runCommand('node', [
        path.join(__dirname, 'crash-test-frontend.js')
      ], { showOutput: true });
      
      this.results.frontendTests.status = 'passed';
      this.log('✅ Frontend tests completed', 'green');
      
    } catch (error) {
      this.results.frontendTests.status = 'failed';
      this.results.frontendTests.errors.push(error.message);
      this.log(`❌ Frontend tests failed: ${error.message}`, 'red');
    }
  }

  generateReport() {
    const endTime = Date.now();
    const duration = ((endTime - this.startTime) / 1000).toFixed(2);
    
    this.log('\n📊 FINAL TEST REPORT', 'bold');
    this.log('='.repeat(60), 'blue');
    
    this.log(`\n⏱️  Total Duration: ${duration} seconds`, 'blue');
    
    // Risultati per categoria
    const categories = [
      { name: 'System Health', key: 'systemHealth', icon: '🏥' },
      { name: 'Code Analysis', key: 'codeAnalysis', icon: '🔍' },
      { name: 'API Tests', key: 'apiTests', icon: '🌐' },
      { name: 'Frontend Tests', key: 'frontendTests', icon: '🖥️' }
    ];
    
    let totalPassed = 0;
    let totalFailed = 0;
    
    categories.forEach(category => {
      const result = this.results[category.key];
      const status = result.status;
      const color = status === 'passed' ? 'green' : status === 'failed' ? 'red' : 'yellow';
      const icon = status === 'passed' ? '✅' : status === 'failed' ? '❌' : '⏳';
      
      this.log(`\n${category.icon} ${category.name}: ${icon} ${status.toUpperCase()}`, color);
      
      if (result.errors.length > 0) {
        result.errors.forEach(error => {
          this.log(`   - ${error}`, 'red');
        });
      }
      
      if (status === 'passed') totalPassed++;
      if (status === 'failed') totalFailed++;
    });
    
    // Riepilogo finale
    this.log('\n📈 SUMMARY:', 'bold');
    this.log(`Passed: ${totalPassed}/${categories.length}`, totalPassed === categories.length ? 'green' : 'yellow');
    this.log(`Failed: ${totalFailed}/${categories.length}`, totalFailed === 0 ? 'green' : 'red');
    
    const successRate = ((totalPassed / categories.length) * 100).toFixed(1);
    this.log(`Success Rate: ${successRate}%`, successRate > 75 ? 'green' : 'red');
    
    // Raccomandazioni
    this.log('\n💡 RECOMMENDATIONS:', 'bold');
    
    if (this.results.systemHealth.status === 'failed') {
      this.log('- Fix system health issues before running other tests', 'yellow');
    }
    
    if (this.results.codeAnalysis.status === 'passed') {
      this.log('- Review the cleanup script generated in tests/cleanup-script.sh', 'yellow');
      this.log('- Remove unused files and console.log statements', 'yellow');
    }
    
    if (this.results.apiTests.status === 'failed') {
      this.log('- Ensure backend server is running on port 8001', 'yellow');
      this.log('- Check database connection and credentials', 'yellow');
    }
    
    if (this.results.frontendTests.status === 'failed') {
      this.log('- Ensure frontend server is running on port 3000', 'yellow');
      this.log('- Check for JavaScript errors in browser console', 'yellow');
    }
    
    if (totalFailed === 0) {
      this.log('\n🎉 ALL TESTS PASSED! System is ready for UI/UX improvements.', 'green');
    } else {
      this.log('\n⚠️  Some tests failed. Fix issues before proceeding with development.', 'yellow');
    }
  }

  async runAllTests() {
    this.log('\n🚀 Starting CMS Complete Test Suite', 'bold');
    this.log('='.repeat(60), 'blue');
    this.log('This will run all crash tests and code analysis', 'blue');
    
    // Esegui tutti i test in sequenza
    await this.checkSystemHealth();
    await this.runCodeAnalysis();
    
    // I test API e Frontend richiedono i server in esecuzione
    if (this.results.systemHealth.status === 'passed') {
      await this.runAPITests();
      await this.runFrontendTests();
    } else {
      this.log('\n⚠️  Skipping API and Frontend tests due to system health issues', 'yellow');
    }
    
    // Genera report finale
    this.generateReport();
  }
}

// Esecuzione dei test
if (require.main === module) {
  const runner = new MasterTestRunner();
  runner.runAllTests().catch(error => {
    console.error('Fatal error running test suite:', error);
    process.exit(1);
  });
}

module.exports = MasterTestRunner;
