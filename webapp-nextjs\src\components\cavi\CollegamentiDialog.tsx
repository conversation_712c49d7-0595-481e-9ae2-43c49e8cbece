'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue} from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle, Zap, CheckCircle } from 'lucide-react'
import { Cavo } from '@/types'
import { caviApi, responsabiliApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'

interface CollegamentiDialogProps {
  open: boolean

interface Responsabile {
  id: number

export default function CollegamentiDialog({
  open,
  onClose,
  cavo,
  onSuccess,
  onError
}: CollegamentiDialogProps) {
  const { cantiere } = useAuth()
  const [selectedResponsabile, setSelectedResponsabile] = useState('')
  const [responsabili, setResponsabili] = useState<Responsabile[]>([])
  const [loading, setLoading] = useState(false)
  const [loadingResponsabili, setLoadingResponsabili] = useState(false)
  const [error, setError] = useState('')

  // Reset form quando si apre il dialog
  useEffect(() => {
    if (open && cavo) {
      setSelectedResponsabile('')
      setError('')
      loadResponsabili()

  }, [open, cavo])

  const loadResponsabili = async () => {
    if (!cantiere) return

    try {
      setLoadingResponsabili(true)
      const response = await responsabiliApi.getResponsabili(cantiere.id_cantiere)
      setResponsabili(response.data)
    } catch (error) {
      setResponsabili([])
    } finally {
      setLoadingResponsabili(false)

  const getStatoCollegamento = () => {
    if (!cavo) return { stato: 'non_collegato', descrizione: 'Non collegato' }
    
    const collegamento = cavo.collegamento || cavo.collegamenti || 0
    
    switch (collegamento) {
      case 1:
        return { stato: 'partenza', descrizione: '🟢⚪ Collegato lato partenza' }
      case 2:
        return { stato: 'arrivo', descrizione: '⚪🟢 Collegato lato arrivo' }
      case 3:
        return { stato: 'completo', descrizione: '🟢🟢 Completamente collegato' }
      default:
        return { stato: 'non_collegato', descrizione: '⚪⚪ Non collegato' }

  const handleCollegaPartenza = async () => {
    if (!cavo || !cantiere) return

    try {
      setLoading(true)
      setError('')

      await caviApi.collegaCavo(
        cantiere.id_cantiere,
        cavo.id_cavo,
        'partenza',
        selectedResponsabile
      )

      onSuccess(`Collegamento lato partenza completato per il cavo ${cavo.id_cavo}`,
      onClose()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il collegamento'
      onError(errorMessage)
    } finally {
      setLoading(false)

  const handleCollegaArrivo = async () => {
    if (!cavo || !cantiere) return

    try {
      setLoading(true)
      setError('')

      await caviApi.collegaCavo(
        cantiere.id_cantiere,
        cavo.id_cavo,
        'arrivo',
        selectedResponsabile
      )

      onSuccess(`Collegamento lato arrivo completato per il cavo ${cavo.id_cavo}`,
      onClose()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il collegamento'
      onError(errorMessage)
    } finally {
      setLoading(false)

  const handleScollega = async (lato?: 'partenza' | 'arrivo') => {
    if (!cavo || !cantiere) return

    try {
      setLoading(true)
      setError('')

      await caviApi.scollegaCavo(
        cantiere.id_cantiere,
        cavo.id_cavo,
        lato
      )

      const latoText = lato ? ` lato ${lato}` : ''
      onSuccess(`,collegamento${latoText} completato per il cavo ${cavo.id_cavo}`)
      onClose()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante lo scollegamento'
      onError(errorMessage)
    } finally {
      setLoading(false)

  if (!cavo) return null

  const statoCollegamento = getStatoCollegamento()
  const isInstalled = (cavo.metri_posati || cavo.metratura_reale || 0) > 0

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Gestione Collegamenti - {cavo.id_cavo}
          </DialogTitle>
          <DialogDescription>
            Gestisci i collegamenti del cavo {cavo.id_cavo}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Stato attuale */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <Label className="text-sm font-medium">Stato Attuale</Label>
            <div className="mt-1 text-lg font-semibold">
              {statoCollegamento.descrizione}
            </div>
          </div>

          {!isInstalled && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Il cavo deve essere installato prima di poter essere collegato.
              </AlertDescription>
            </Alert>
          )}

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {isInstalled && (
            <>
              {/* Selezione responsabile */}
              <div className="space-y-2">
                <Label htmlFor="responsabile">Responsabile Collegamento</Label>
                <Select
                  value={selectedResponsabile}
                  onValueChange={setSelectedResponsabile}
                  disabled={loadingResponsabili}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Seleziona responsabile..." />
                  </SelectTrigger>
                  <SelectContent>
                    {responsabili.map((resp) => (
                      <SelectItem key={resp.id} value={resp.nome_responsabile}>
                        {resp.nome_responsabile}
                        {resp.numero_telefono && ` - ${resp.numero_telefono}`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Azioni di collegamento */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">Azioni Disponibili</Label>
                
                <div className="grid grid-cols-2 gap-2">
                  {statoCollegamento.stato !== 'partenza' && statoCollegamento.stato !== 'completo' && (
                    <Button
                      onClick={handleCollegaPartenza}
                      disabled={loading || !selectedResponsabile}
                      className="w-full"
                    >
                      {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Zap className="h-4 w-4 mr-2" />}
                      Collega Partenza
                    </Button>
                  )}

                  {statoCollegamento.stato !== 'arrivo' && statoCollegamento.stato !== 'completo' && (
                    <Button
                      onClick={handleCollegaArrivo}
                      disabled={loading || !selectedResponsabile}
                      className="w-full"
                    >
                      {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Zap className="h-4 w-4 mr-2" />}
                      Collega Arrivo
                    </Button>
                  )}
                </div>

                {statoCollegamento.stato !== 'non_collegato' && (
                  <div className="space-y-2">
                    <Button
                      onClick={() => handleScollega()}
                      disabled={loading}
                      variant="destructive"
                      className="w-full"
                    >
                      {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <AlertCircle className="h-4 w-4 mr-2" />}
                      Scollega Completamente
                    </Button>
                  </div>
                )}
              </div>
            </>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            Chiudi
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
