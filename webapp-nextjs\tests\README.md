# CMS Crash Test Suite

Questa directory contiene una suite completa di test per verificare il funzionamento del sistema CMS e identificare problemi prima di continuare con lo sviluppo UI/UX.

## 🎯 Obiettivi

- **Verificare funzionalità core**: Test delle API backend e frontend
- **Identificare problemi**: Crash test per trovare errori nascosti
- **Pulire il codice**: Analisi di file inutilizzati e codice da ottimizzare
- **Preparare per UI/UX**: Assicurarsi che la base sia solida prima dei miglioramenti

## 📁 File di Test

### `run-all-tests.js` - Master Test Runner
Script principale che esegue tutti i test in sequenza:
- ✅ System Health Check
- 🔍 Code Analysis  
- 🌐 API Tests
- 🖥️ Frontend Tests

### `crash-test-api.js` - Test API Backend
Verifica tutte le API del backend:
- Connessione server
- Autenticazione
- API Cantieri, Cavi, <PERSON><PERSON>, Comande
- Reports e Database

### `crash-test-frontend.js` - Test Frontend
Test completo dell'interfaccia utente:
- Caricamento pagine
- Login flow
- Navigazione
- Form validation
- Responsive design

### `analyze-unused-files.js` - Analisi Codice
Analizza il codice per trovare:
- File non utilizzati
- Console.log da rimuovere
- TODO/FIXME comments
- File troppo grandi
- Import duplicati

## 🚀 Come Eseguire i Test

### Prerequisiti
1. **Backend in esecuzione** su porta 8001:
   ```bash
   cd c:\CMS
   python main.py
   ```

2. **Frontend in esecuzione** su porta 3000:
   ```bash
   cd c:\CMS\webapp-nextjs
   npm run dev
   ```

3. **Installare dipendenze test** (se necessario):
   ```bash
   npm install puppeteer
   ```

### Esecuzione

#### Test Completo (Raccomandato)
```bash
npm run test
# oppure
node tests/run-all-tests.js
```

#### Test Singoli
```bash
# Solo analisi codice
npm run analyze

# Solo test API
npm run test:api

# Solo test frontend  
npm run test:frontend
```

## 📊 Interpretazione Risultati

### System Health ✅
- Verifica Node.js, npm, dipendenze
- Controlla file di configurazione
- **Se fallisce**: Risolvere prima di procedere

### Code Analysis 🔍
- Genera `cleanup-script.sh` con azioni suggerite
- Identifica file da rimuovere
- **Azione**: Rivedere e applicare pulizia

### API Tests 🌐
- Testa tutte le funzionalità backend
- **Se fallisce**: Controllare server backend e database

### Frontend Tests 🖥️
- Testa navigazione e UI
- **Se fallisce**: Controllare errori JavaScript

## 🧹 Pulizia Codice

Dopo l'analisi, viene generato `tests/cleanup-script.sh`:

```bash
# Rivedere il file prima di eseguire
cat tests/cleanup-script.sh

# Applicare pulizia manualmente o con cautela
# bash tests/cleanup-script.sh
```

## 📈 Metriche di Successo

- **System Health**: 100% (critico)
- **Code Analysis**: Sempre passa (informativo)
- **API Tests**: >80% (importante)
- **Frontend Tests**: >70% (accettabile)

## 🔧 Risoluzione Problemi

### Backend non risponde
```bash
# Verificare che il backend sia in esecuzione
curl http://localhost:8001/health

# Riavviare se necessario
cd c:\CMS
python main.py
```

### Frontend non carica
```bash
# Verificare che il frontend sia in esecuzione
curl http://localhost:3000

# Riavviare se necessario
cd c:\CMS\webapp-nextjs
npm run dev
```

### Errori Puppeteer
```bash
# Reinstallare Puppeteer
npm uninstall puppeteer
npm install puppeteer
```

### Database non connesso
- Verificare PostgreSQL in esecuzione
- Controllare credenziali in `modules/database_pg.py`
- Testare connessione manuale

## 📝 Prossimi Passi

Dopo aver eseguito i test con successo:

1. **Applicare pulizia codice** suggerita
2. **Risolvere errori critici** identificati
3. **Documentare problemi noti** per riferimento futuro
4. **Procedere con miglioramenti UI/UX** su base solida

## 🎯 Obiettivo Finale

Raggiungere un **Success Rate >85%** prima di procedere con:
- Miglioramenti UI/UX
- Nuove funzionalità
- Ottimizzazioni performance
- Deploy in produzione

---

**Nota**: Questi test sono progettati per essere eseguiti in ambiente di sviluppo. Non eseguire in produzione senza adeguate precauzioni.
