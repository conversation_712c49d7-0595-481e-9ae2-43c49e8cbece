/**
 * SCRIPT DI RIPRISTINO EMERGENZA
 * Ripristina COMPLETAMENTE la sintassi corretta rimuovendo tutti i console.log malformati
 */

const fs = require('fs');
const path = require('path');

class EmergencyRestore {
  constructor() {
    this.srcDir = path.join(__dirname, '..', 'src');
    this.restoredFiles = 0;
    this.totalFixes = 0;
  }

  log(message, color = 'reset') {
    const colors = {
      green: '\x1b[32m',
      red: '\x1b[31m',
      yellow: '\x1b[33m',
      blue: '\x1b[34m',
      reset: '\x1b[0m',
      bold: '\x1b[1m'
    };
    console.log(`${colors[color]}${message}${colors.reset}`);
  }

  findFiles(dir, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
    let files = [];
    
    try {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          if (!item.startsWith('.') && item !== 'node_modules') {
            files = files.concat(this.findFiles(fullPath, extensions));
          }
        } else if (extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      this.log(`❌ Errore lettura directory ${dir}: ${error.message}`, 'red');
    }
    
    return files;
  }

  restoreFile(filePath) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      const originalContent = content;
      let fixes = 0;

      // RIMOZIONE COMPLETA DI TUTTI I CONSOLE.LOG MALFORMATI

      // 1. Rimuovi console.log({ seguiti da qualsiasi cosa
      content = content.replace(/console\.log\(\{[^}]*\}?\)?[,;]?\s*/g, () => {
        fixes++;
        return '';
      });

      // 2. Rimuovi righe che contengono solo console.log
      content = content.replace(/^\s*console\.log\([^)]*\)\s*[,;]?\s*$/gm, () => {
        fixes++;
        return '';
      });

      // 3. Rimuovi console.log in mezzo a oggetti
      content = content.replace(/,\s*console\.log\([^)]*\)\s*,?/g, () => {
        fixes++;
        return '';
      });

      // 4. Correggi backtick malformati (`) con virgole
      content = content.replace(/(\w+):\s*([^,}]+)`/g, (match, prop, value) => {
        fixes++;
        return `${prop}: ${value},`;
      });

      // 5. Correggi template literals rotti
      content = content.replace(/`([^`]*)`([^,}\s])/g, (match, template, next) => {
        fixes++;
        return `\`${template}\`,`;
      });

      // 6. Rimuovi parentesi graffe orfane
      content = content.replace(/^\s*\}\s*$/gm, () => {
        fixes++;
        return '';
      });

      // 7. Rimuovi virgole doppie
      content = content.replace(/,\s*,/g, ',');

      // 8. Rimuovi virgole prima di parentesi graffe chiuse
      content = content.replace(/,(\s*\})/g, '$1');

      // 9. Rimuovi virgole all'inizio di oggetti
      content = content.replace(/\{\s*,/g, '{');

      // 10. Correggi spazi multipli
      content = content.replace(/\n\s*\n\s*\n/g, '\n\n');

      // 11. Rimuovi righe vuote eccessive
      content = content.replace(/\n{3,}/g, '\n\n');

      if (fixes > 0) {
        fs.writeFileSync(filePath, content, 'utf8');
        this.restoredFiles++;
        this.totalFixes += fixes;
        this.log(`✅ ${path.relative(this.srcDir, filePath)}: ${fixes} correzioni`, 'green');
      }

      return fixes;
    } catch (error) {
      this.log(`❌ Errore ripristino ${filePath}: ${error.message}`, 'red');
      return 0;
    }
  }

  run() {
    this.log('\n🚨 RIPRISTINO EMERGENZA SISTEMA', 'bold');
    this.log('Rimuove TUTTI i console.log malformati e ripristina la sintassi', 'yellow');
    
    const files = this.findFiles(this.srcDir);
    this.log(`\n📁 Ripristinando ${files.length} file...`, 'blue');
    
    files.forEach(file => {
      this.restoreFile(file);
    });
    
    this.log('\n📊 RIEPILOGO RIPRISTINO', 'bold');
    this.log('='.repeat(50), 'blue');
    this.log(`✅ File ripristinati: ${this.restoredFiles}`, 'green');
    this.log(`🔧 Correzioni totali: ${this.totalFixes}`, 'green');
    
    if (this.totalFixes === 0) {
      this.log('\n✅ Sistema già pulito!', 'green');
    } else {
      this.log('\n🎯 Sistema ripristinato!', 'green');
      this.log('Il codice dovrebbe ora essere tornato allo stato funzionante.', 'blue');
    }
  }
}

// Esecuzione dello script
if (require.main === module) {
  const restorer = new EmergencyRestore();
  restorer.run();
}

module.exports = EmergencyRestore;
