'use client'

import { useState, useEffect } from 'react'

export interface Toast {
  id: string
  title?: string
  description?: string
  variant?: 'default' | 'destructive'
  duration?: number

interface ToastState {
  toasts: Toast[]

const TOAST_LIMIT = 1
const TOAST_REMOVE_DELAY = 1000000

let count = 0

function genId() {
  count = (count + 1) % Number.MAX_VALUE
  return count.toString()

const toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()

const addToRemoveQueue = (toastId: string) => {
  if (toastTimeouts.has(toastId)) {
    return

  const timeout = setTimeout(() => {
    toastTimeouts.delete(toastId)
    dispatch({
      type: 'REMOVE_TOAST')
  }, TOAST_REMOVE_DELAY)

  toastTimeouts.set(toastId, timeout)

export const reducer = (state: ToastState, action: any): ToastState => {
  switch (action.type) {
    case 'ADD_TOAST':
      return {
        ...state

    case 'UPDATE_TOAST':
      return {
        ...state : t
        )}

    case 'DISMISS_TOAST': {
      const { toastId } = action

      if (toastId) {
        addToRemoveQueue(toastId)
      } else {
        state.toasts.forEach((toast) => {
          addToRemoveQueue(toast.id)
        })

      return {
        ...state
            : t
        )}

    case 'REMOVE_TOAST':
      if (action.toastId === undefined) {
        return {
          ...state

      return {
        ...state

const listeners: Array<(state: ToastState) => void> = []

let memoryState: ToastState = { toasts: [] }

function dispatch(action: any) {
  memoryState = reducer(memoryState, action)
  listeners.forEach((listener) => {
    listener(memoryState)
  })

type Toast2 = Omit<Toast, 'id'>

function toast({ ...props }: Toast2) {
  const id = genId()

  const update = (props: Partial<Toast>) =>
    dispatch({
      type: 'UPDATE_TOAST'})
  const dismiss = () => dispatch({ type: 'DISMISS_TOAST', toastId: id })

  dispatch({
    type: 'ADD_TOAST'}})

  return {
    id: id,
    dismiss,
    update}

function useToast() {
  const [state, setState] = useState<ToastState>(memoryState)

  useEffect(() => {
    listeners.push(setState)
    return () => {
      const index = listeners.indexOf(setState)
      if (index > -1) {
        listeners.splice(index, 1)

  }, [])

  return {
    ...state,
    toast)}

export { useToast, toast }
