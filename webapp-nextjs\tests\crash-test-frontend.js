/**
 * CRASH TEST SUITE - Frontend Testing
 * Test completo delle funzionalità frontend del sistema CMS
 */

const puppeteer = require('puppeteer');
const path = require('path');

// Configurazione base
const BASE_URL = 'http://localhost:3000';
const TEST_TIMEOUT = 30000;

// Colori per output console
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

class CrashTestFrontend {
  constructor() {
    this.results = {
      total: 0,
      passed: 0,
      failed: 0,
      errors: []
    };
    this.browser = null;
    this.page = null;
  }

  log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
  }

  async setup() {
    this.browser = await puppeteer.launch({
      headless: false, // Mostra il browser per debug
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    this.page = await this.browser.newPage();
    
    // Intercetta errori console
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        this.log(`Console Error: ${msg.text()}`, 'red');
      }
    });
    
    // Intercetta errori di rete
    this.page.on('response', response => {
      if (response.status() >= 400) {
        this.log(`Network Error: ${response.url()} - ${response.status()}`, 'red');
      }
    });
  }

  async teardown() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  async runTest(testName, testFunction) {
    this.results.total++;
    try {
      this.log(`\n🧪 Testing: ${testName}`, 'blue');
      await testFunction();
      this.results.passed++;
      this.log(`✅ PASSED: ${testName}`, 'green');
    } catch (error) {
      this.results.failed++;
      this.results.errors.push({ test: testName, error: error.message });
      this.log(`❌ FAILED: ${testName}`, 'red');
      this.log(`   Error: ${error.message}`, 'red');
    }
  }

  async testPageLoad() {
    await this.page.goto(BASE_URL, { waitUntil: 'networkidle2', timeout: TEST_TIMEOUT });
    
    // Verifica che la pagina sia caricata
    const title = await this.page.title();
    if (!title || title.includes('Error')) {
      throw new Error('Page failed to load properly');
    }
  }

  async testLogin() {
    await this.page.goto(`${BASE_URL}/login`, { waitUntil: 'networkidle2' });
    
    // Compila form di login
    await this.page.waitForSelector('input[name="username"]', { timeout: 5000 });
    await this.page.type('input[name="username"]', 'admin');
    await this.page.type('input[name="password"]', 'admin123');
    
    // Clicca login
    await this.page.click('button[type="submit"]');
    
    // Aspetta redirect
    await this.page.waitForNavigation({ waitUntil: 'networkidle2', timeout: TEST_TIMEOUT });
    
    // Verifica che siamo nella dashboard
    const url = this.page.url();
    if (!url.includes('/cantieri') && !url.includes('/dashboard')) {
      throw new Error('Login failed - not redirected to dashboard');
    }
  }

  async testNavigation() {
    // Test navigazione principale
    const navItems = [
      { selector: 'a[href*="/cantieri"]', name: 'Cantieri' },
      { selector: 'a[href*="/cavi"]', name: 'Cavi' },
      { selector: 'a[href*="/parco-cavi"]', name: 'Parco Cavi' },
      { selector: 'a[href*="/comande"]', name: 'Comande' },
      { selector: 'a[href*="/reports"]', name: 'Reports' }
    ];

    for (const item of navItems) {
      try {
        await this.page.waitForSelector(item.selector, { timeout: 5000 });
        await this.page.click(item.selector);
        await this.page.waitForLoadState('networkidle', { timeout: 10000 });
        this.log(`  ✓ Navigation to ${item.name} works`, 'green');
      } catch (error) {
        throw new Error(`Navigation to ${item.name} failed: ${error.message}`);
      }
    }
  }

  async testCaviPage() {
    await this.page.goto(`${BASE_URL}/cavi`, { waitUntil: 'networkidle2' });
    
    // Verifica che la tabella cavi sia presente
    await this.page.waitForSelector('table', { timeout: 10000 });
    
    // Verifica che ci siano dati o almeno headers
    const tableRows = await this.page.$$('table tr');
    if (tableRows.length === 0) {
      throw new Error('Cavi table is empty or not loading');
    }
  }

  async testBobinePage() {
    await this.page.goto(`${BASE_URL}/parco-cavi`, { waitUntil: 'networkidle2' });
    
    // Verifica che la pagina bobine sia caricata
    await this.page.waitForSelector('[data-testid="bobine-container"], table, .bobine-list', { timeout: 10000 });
  }

  async testComandePage() {
    await this.page.goto(`${BASE_URL}/comande`, { waitUntil: 'networkidle2' });
    
    // Verifica che la pagina comande sia caricata
    await this.page.waitForSelector('[data-testid="comande-container"], table, .comande-list', { timeout: 10000 });
  }

  async testReportsPage() {
    await this.page.goto(`${BASE_URL}/reports`, { waitUntil: 'networkidle2' });
    
    // Verifica che la pagina reports sia caricata
    await this.page.waitForSelector('[data-testid="reports-container"], .reports-content', { timeout: 10000 });
  }

  async testResponsiveDesign() {
    // Test mobile viewport
    await this.page.setViewport({ width: 375, height: 667 });
    await this.page.reload({ waitUntil: 'networkidle2' });
    
    // Verifica che il menu mobile funzioni
    const mobileMenuExists = await this.page.$('[data-testid="mobile-menu"], .mobile-menu, button[aria-label*="menu"]');
    if (!mobileMenuExists) {
      this.log('  ⚠️  Mobile menu not found - check responsive design', 'yellow');
    }
    
    // Ripristina desktop viewport
    await this.page.setViewport({ width: 1920, height: 1080 });
  }

  async testFormValidation() {
    // Test form validation su pagina cavi
    await this.page.goto(`${BASE_URL}/cavi`, { waitUntil: 'networkidle2' });
    
    // Cerca bottone "Aggiungi Cavo" o simile
    const addButton = await this.page.$('button:has-text("Aggiungi"), button[data-testid*="add"], .add-button');
    if (addButton) {
      await addButton.click();
      
      // Verifica che si apra un dialog/form
      await this.page.waitForSelector('dialog, .modal, .form-container', { timeout: 5000 });
      
      // Prova a submit senza dati
      const submitButton = await this.page.$('button[type="submit"], button:has-text("Salva")');
      if (submitButton) {
        await submitButton.click();
        
        // Verifica che appaiano errori di validazione
        const errorExists = await this.page.$('.error, .invalid, [aria-invalid="true"]');
        if (!errorExists) {
          this.log('  ⚠️  Form validation might not be working', 'yellow');
        }
      }
    }
  }

  async runAllTests() {
    this.log('\n🚀 Starting CMS Frontend Crash Test Suite', 'bold');
    this.log('='.repeat(50), 'blue');

    await this.setup();

    try {
      // Test di base
      await this.runTest('Page Load', () => this.testPageLoad());
      await this.runTest('Login Flow', () => this.testLogin());
      
      // Test navigazione
      await this.runTest('Navigation', () => this.testNavigation());
      
      // Test pagine principali
      await this.runTest('Cavi Page', () => this.testCaviPage());
      await this.runTest('Bobine Page', () => this.testBobinePage());
      await this.runTest('Comande Page', () => this.testComandePage());
      await this.runTest('Reports Page', () => this.testReportsPage());
      
      // Test UI/UX
      await this.runTest('Responsive Design', () => this.testResponsiveDesign());
      await this.runTest('Form Validation', () => this.testFormValidation());

    } finally {
      await this.teardown();
    }

    // Risultati finali
    this.printResults();
  }

  printResults() {
    this.log('\n📊 FRONTEND TEST RESULTS', 'bold');
    this.log('='.repeat(50), 'blue');
    this.log(`Total Tests: ${this.results.total}`, 'blue');
    this.log(`Passed: ${this.results.passed}`, 'green');
    this.log(`Failed: ${this.results.failed}`, 'red');
    
    if (this.results.errors.length > 0) {
      this.log('\n❌ ERRORS:', 'red');
      this.results.errors.forEach(error => {
        this.log(`  - ${error.test}: ${error.error}`, 'red');
      });
    }
    
    const successRate = ((this.results.passed / this.results.total) * 100).toFixed(1);
    this.log(`\nSuccess Rate: ${successRate}%`, successRate > 80 ? 'green' : 'red');
    
    if (this.results.failed === 0) {
      this.log('\n🎉 ALL FRONTEND TESTS PASSED!', 'green');
    } else {
      this.log('\n⚠️  SOME FRONTEND TESTS FAILED - CHECK ERRORS ABOVE', 'yellow');
    }
  }
}

// Esecuzione dei test
if (require.main === module) {
  const tester = new CrashTestFrontend();
  tester.runAllTests().catch(error => {
    console.error('Fatal error running frontend tests:', error);
    process.exit(1);
  });
}

module.exports = CrashTestFrontend;
