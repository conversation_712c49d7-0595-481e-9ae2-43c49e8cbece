import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Proxy la richiesta al backend FastAPI
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'
    
    const response = await fetch(,${backendUrl}/api/password/confirm-password-reset`, {
      method: 'POST',
      body: JSON.stringify(body)
    })

    const data = await response.json()

    return NextResponse.json(data, { 
      status: response.status
    })

  } catch (error) {
    return NextResponse.json(
      { 
        success: false, 
      { status: 500 }
    )

