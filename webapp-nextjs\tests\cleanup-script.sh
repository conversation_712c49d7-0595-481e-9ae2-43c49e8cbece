#!/bin/bash
# Auto-generated cleanup script
# Review before executing!

# Remove unused files
# rm "src\app\admin\page.tsx"
# rm "src\app\api\cavi\bulk-delete\route.ts"
# rm "src\app\api\cavi\bulk-status\route.ts"
# rm "src\app\api\cavi\export\route.ts"
# rm "src\app\api\password\confirm-password-reset\route.ts"
# rm "src\app\api\password\request-password-reset\route.ts"
# rm "src\app\api\password\validate-password\route.ts"
# rm "src\app\api\password\verify-reset-token\route.ts"
# rm "src\app\cantieri\page.tsx"
# rm "src\app\cantieri\[id]\page.tsx"
# rm "src\app\cavi\page.tsx"
# rm "src\app\certificazioni\page.tsx"
# rm "src\app\comande\page.tsx"
# rm "src\app\debug-user\page.tsx"
# rm "src\app\demo-buttons\page.tsx"
# rm "src\app\forgot-password\page.tsx"
# rm "src\app\login\page.tsx"
# rm "src\app\parco-cavi\page.tsx"
# rm "src\app\productivity\page.tsx"
# rm "src\app\reports\page.tsx"
# rm "src\app\reset-password\page.tsx"
# rm "src\app\test\page.tsx"
# rm "src\app\test-password-reset\page.tsx"
# rm "src\components\admin\DatabaseView.tsx"
# rm "src\components\admin\ImpersonateUser.tsx"
# rm "src\components\admin\QuickImpersonate.tsx"
# rm "src\components\admin\ResetDatabase.tsx"
# rm "src\components\admin\TipologieCaviManager.tsx"
# rm "src\components\admin\UserForm.tsx"
# rm "src\components\auth\PasswordManagement.tsx"
# rm "src\components\auth\PasswordResetConfirm.tsx"
# rm "src\components\auth\SecureLoginForm.tsx"
# rm "src\components\bobine\AggiungiCaviDialogSimple.tsx"
# rm "src\components\bobine\BobineStatistics.tsx"
# rm "src\components\bobine\CreaBobinaDialog.tsx"
# rm "src\components\bobine\EliminaBobinaDialog.tsx"
# rm "src\components\bobine\ModificaBobinaDialog.tsx"
# rm "src\components\bobine\VisualizzaBobinaDialog.tsx"
# rm "src\components\cavi\CaviContextMenu.tsx"
# rm "src\components\cavi\CaviFilters.tsx"
# rm "src\components\cavi\CaviStatistics.tsx"
# rm "src\components\cavi\CaviTable.tsx"
# rm "src\components\cavi\CertificazioneDialog.tsx"
# rm "src\components\cavi\CollegamentiDialog.tsx"
# rm "src\components\cavi\CreaComandaDialog.tsx"
# rm "src\components\cavi\ExportDataDialog.tsx"
# rm "src\components\cavi\ImportExcelDialog.tsx"
# rm "src\components\cavi\IncompatibleReelDialog.tsx"
# rm "src\components\cavi\InserisciMetriDialog.tsx"
# rm "src\components\comande\CreaComandaDialog.tsx"
# rm "src\components\comande\DettagliComandaDialog.tsx"
# rm "src\components\comande\GestisciResponsabiliDialog.tsx"
# rm "src\components\comande\InserisciMetriDialog.tsx"
# rm "src\components\comande\InserisciMetriPosatiDialog.tsx"
# rm "src\components\common\FilterableTable.tsx"
# rm "src\components\common\TruncatedText.tsx"
# rm "src\components\debug\BobinaCompatibilityDebug.tsx"
# rm "src\components\debug\CaviDebugDialog.tsx"
# rm "src\components\debug\MetriPosatiDebugDialog.tsx"
# rm "src\components\layout\Navbar.tsx"
# rm "src\components\ui\alert.tsx"
# rm "src\components\ui\animated-button.tsx"
# rm "src\components\ui\badge.tsx"
# rm "src\components\ui\button.tsx"
# rm "src\components\ui\card.tsx"
# rm "src\components\ui\checkbox.tsx"
# rm "src\components\ui\collapsible.tsx"
# rm "src\components\ui\compact-actions-dropdown.tsx"
# rm "src\components\ui\context-menu-custom.tsx"
# rm "src\components\ui\context-menu.tsx"
# rm "src\components\ui\dialog.tsx"
# rm "src\components\ui\dropdown-menu.tsx"
# rm "src\components\ui\inline-actions-menu.tsx"
# rm "src\components\ui\input.tsx"
# rm "src\components\ui\label.tsx"
# rm "src\components\ui\popover.tsx"
# rm "src\components\ui\progress.tsx"
# rm "src\components\ui\select.tsx"
# rm "src\components\ui\sheet.tsx"
# rm "src\components\ui\simple-actions.tsx"
# rm "src\components\ui\switch.tsx"
# rm "src\components\ui\table.tsx"
# rm "src\components\ui\tabs.tsx"
# rm "src\components\ui\textarea.tsx"
# rm "src\components\ui\toaster.tsx"
# rm "src\contexts\AuthContext.tsx"
# rm "src\hooks\use-toast.ts"
# rm "src\hooks\useSecurityMonitoring.ts"
# rm "src\lib\api.ts"
# rm "src\lib\utils.ts"
# rm "src\types\index.ts"
# rm "src\utils\bobineUtils.ts"
# rm "src\utils\comandeValidation.ts"
# rm "src\utils\securityValidation.ts"
# rm "src\utils\softColors.ts"

# Files with console.log to clean:
# Clean console.log from src\app\admin\page.tsx
# Clean console.log from src\app\api\cavi\bulk-delete\route.ts
# Clean console.log from src\app\api\cavi\bulk-status\route.ts
# Clean console.log from src\app\api\cavi\export\route.ts
# Clean console.log from src\app\api\password\confirm-password-reset\route.ts
# Clean console.log from src\app\api\password\request-password-reset\route.ts
# Clean console.log from src\app\api\password\validate-password\route.ts
# Clean console.log from src\app\api\password\verify-reset-token\route.ts
# Clean console.log from src\app\cantieri\page.tsx
# Clean console.log from src\app\cantieri\[id]\page.tsx
# Clean console.log from src\app\cavi\page.tsx
# Clean console.log from src\app\certificazioni\page.tsx
# Clean console.log from src\app\comande\page.tsx
# Clean console.log from src\app\debug-user\page.tsx
# Clean console.log from src\app\demo-buttons\page.tsx
# Clean console.log from src\app\parco-cavi\page.tsx
# Clean console.log from src\app\reports\page.tsx
# Clean console.log from src\app\test-password-reset\page.tsx
# Clean console.log from src\components\admin\DatabaseView.tsx
# Clean console.log from src\components\admin\ImpersonateUser.tsx
# Clean console.log from src\components\admin\QuickImpersonate.tsx
# Clean console.log from src\components\auth\PasswordManagement.tsx
# Clean console.log from src\components\auth\PasswordResetConfirm.tsx
# Clean console.log from src\components\auth\SecureLoginForm.tsx
# Clean console.log from src\components\bobine\AggiungiCaviDialogSimple.tsx
# Clean console.log from src\components\bobine\CreaBobinaDialog.tsx
# Clean console.log from src\components\bobine\EliminaBobinaDialog.tsx
# Clean console.log from src\components\bobine\ModificaBobinaDialog.tsx
# Clean console.log from src\components\cavi\CaviTable.tsx
# Clean console.log from src\components\cavi\CertificazioneDialog.tsx
# Clean console.log from src\components\cavi\CollegamentiDialog.tsx
# Clean console.log from src\components\cavi\CreaComandaDialog.tsx
# Clean console.log from src\components\cavi\ExportDataDialog.tsx
# Clean console.log from src\components\cavi\ImportExcelDialog.tsx
# Clean console.log from src\components\cavi\InserisciMetriDialog.tsx
# Clean console.log from src\components\cavi\ModificaBobinaDialog.tsx
# Clean console.log from src\components\comande\CreaComandaDialog.tsx
# Clean console.log from src\components\comande\DettagliComandaDialog.tsx
# Clean console.log from src\components\comande\GestisciResponsabiliDialog.tsx
# Clean console.log from src\components\comande\InserisciMetriPosatiDialog.tsx
# Clean console.log from src\components\debug\BobinaCompatibilityDebug.tsx
# Clean console.log from src\components\debug\CaviDebugDialog.tsx
# Clean console.log from src\components\ui\compact-actions-dropdown.tsx
# Clean console.log from src\components\ui\simple-actions.tsx
# Clean console.log from src\contexts\AuthContext.tsx
# Clean console.log from src\hooks\useSecurityMonitoring.ts
# Clean console.log from src\middleware.ts

