/**
 * IDENTIFICAZIONE FILE VERAMENTE INUTILIZZATI
 * Analisi più intelligente che esclude pagine Next.js e componenti essenziali
 */

const fs = require('fs');
const path = require('path');

class TrulyUnusedFinder {
  constructor() {
    this.srcDir = path.join(__dirname, '../src');
    this.results = {
      trulyUnused: [],
      nextjsPages: [],
      essentialComponents: [],
      apiRoutes: [],
      debugComponents: []
    };
  }

  log(message, color = 'reset') {
    const colors = {
      green: '\x1b[32m',
      red: '\x1b[31m',
      yellow: '\x1b[33m',
      blue: '\x1b[34m',
      reset: '\x1b[0m',
      bold: '\x1b[1m'
    };
    console.log(`${colors[color]}${message}${colors.reset}`);
  }

  getAllFiles(dir, fileList = []) {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        if (!file.startsWith('.') && file !== 'node_modules') {
          this.getAllFiles(filePath, fileList);
        }
      } else if (file.endsWith('.tsx') || file.endsWith('.ts') || file.endsWith('.js') || file.endsWith('.jsx')) {
        fileList.push(filePath);
      }
    });
    
    return fileList;
  }

  isNextJSPage(filePath) {
    // Pagine Next.js sono caricate automaticamente dal router
    return (
      filePath.includes('/app/') && 
      (filePath.endsWith('/page.tsx') || filePath.endsWith('/layout.tsx'))
    );
  }

  isAPIRoute(filePath) {
    // API routes sono endpoint REST
    return filePath.includes('/app/api/') && filePath.endsWith('/route.ts');
  }

  isEssentialComponent(filePath) {
    // Componenti UI essenziali (shadcn/ui)
    if (filePath.includes('/components/ui/')) return true;
    
    // Componenti di layout
    if (filePath.includes('/components/layout/')) return true;
    
    // Context e hooks
    if (filePath.includes('/contexts/') || filePath.includes('/hooks/')) return true;
    
    // Utilities e types
    if (filePath.includes('/lib/') || filePath.includes('/types/') || filePath.includes('/utils/')) return true;
    
    // Middleware
    if (filePath.endsWith('middleware.ts')) return true;
    
    return false;
  }

  isDebugComponent(filePath) {
    return filePath.includes('/components/debug/');
  }

  findImportsInFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const imports = [];
      
      // Regex per trovare import
      const importRegex = /import.*from\s+['"`]([^'"`]+)['"`]/g;
      let match;
      
      while ((match = importRegex.exec(content)) !== null) {
        const importPath = match[1];
        
        // Solo import relativi
        if (importPath.startsWith('.')) {
          imports.push(importPath);
        }
      }
      
      return imports;
    } catch (error) {
      return [];
    }
  }

  resolveImportPath(basePath, importPath) {
    const baseDir = path.dirname(basePath);
    const resolvedPath = path.resolve(baseDir, importPath);
    
    // Prova diverse estensioni
    const extensions = ['.tsx', '.ts', '.js', '.jsx'];
    
    for (const ext of extensions) {
      const fullPath = resolvedPath + ext;
      if (fs.existsSync(fullPath)) {
        return fullPath;
      }
    }
    
    // Prova index files
    for (const ext of extensions) {
      const indexPath = path.join(resolvedPath, 'index' + ext);
      if (fs.existsSync(indexPath)) {
        return indexPath;
      }
    }
    
    return null;
  }

  analyzeUsage() {
    this.log('\n🔍 Analizzando utilizzo file...', 'blue');
    
    const allFiles = this.getAllFiles(this.srcDir);
    const usedFiles = new Set();
    
    // Trova tutti gli import
    allFiles.forEach(filePath => {
      const imports = this.findImportsInFile(filePath);
      
      imports.forEach(importPath => {
        const resolvedPath = this.resolveImportPath(filePath, importPath);
        if (resolvedPath) {
          usedFiles.add(resolvedPath);
        }
      });
    });

    // Categorizza i file
    allFiles.forEach(filePath => {
      const relativePath = filePath.replace(this.srcDir, '');
      const isUsed = usedFiles.has(filePath);
      
      if (this.isNextJSPage(filePath)) {
        this.results.nextjsPages.push({ path: relativePath, used: isUsed });
      } else if (this.isAPIRoute(filePath)) {
        this.results.apiRoutes.push({ path: relativePath, used: isUsed });
      } else if (this.isEssentialComponent(filePath)) {
        this.results.essentialComponents.push({ path: relativePath, used: isUsed });
      } else if (this.isDebugComponent(filePath)) {
        this.results.debugComponents.push({ path: relativePath, used: isUsed });
      } else if (!isUsed) {
        this.results.trulyUnused.push(relativePath);
      }
    });
  }

  printResults() {
    this.log('\n📊 ANALISI FILE INUTILIZZATI', 'bold');
    this.log('='.repeat(60), 'blue');
    
    // File veramente inutilizzati
    this.log(`\n🗑️  File Veramente Inutilizzati (${this.results.trulyUnused.length}):`, 'red');
    if (this.results.trulyUnused.length > 0) {
      this.results.trulyUnused.forEach(file => {
        this.log(`  ❌ src${file}`, 'red');
      });
    } else {
      this.log('  ✅ Nessun file veramente inutilizzato trovato', 'green');
    }
    
    // Pagine Next.js
    this.log(`\n📄 Pagine Next.js (${this.results.nextjsPages.length}):`, 'blue');
    this.results.nextjsPages.forEach(item => {
      const status = item.used ? '✅ Utilizzata' : '⚠️  Non referenziata (ma caricata dal router)';
      this.log(`  ${status}: src${item.path}`, item.used ? 'green' : 'yellow');
    });
    
    // API Routes
    this.log(`\n🌐 API Routes (${this.results.apiRoutes.length}):`, 'blue');
    this.results.apiRoutes.forEach(item => {
      const status = item.used ? '✅ Utilizzata' : '⚠️  Non referenziata (ma endpoint REST)';
      this.log(`  ${status}: src${item.path}`, item.used ? 'green' : 'yellow');
    });
    
    // Componenti essenziali
    this.log(`\n🔧 Componenti Essenziali (${this.results.essentialComponents.length}):`, 'blue');
    const unusedEssential = this.results.essentialComponents.filter(item => !item.used);
    if (unusedEssential.length > 0) {
      this.log('  ⚠️  Componenti essenziali non utilizzati:', 'yellow');
      unusedEssential.forEach(item => {
        this.log(`    - src${item.path}`, 'yellow');
      });
    } else {
      this.log('  ✅ Tutti i componenti essenziali sono utilizzati', 'green');
    }
    
    // Componenti debug
    this.log(`\n🐛 Componenti Debug (${this.results.debugComponents.length}):`, 'blue');
    this.results.debugComponents.forEach(item => {
      const status = item.used ? '✅ Utilizzato' : '⚠️  Non utilizzato (ma utile per troubleshooting)';
      this.log(`  ${status}: src${item.path}`, item.used ? 'green' : 'yellow');
    });
  }

  generateCleanupRecommendations() {
    this.log('\n💡 RACCOMANDAZIONI PULIZIA', 'bold');
    this.log('='.repeat(60), 'blue');
    
    if (this.results.trulyUnused.length > 0) {
      this.log('\n🗑️  File da rimuovere:', 'red');
      this.results.trulyUnused.forEach(file => {
        this.log(`rm "src${file}"`, 'red');
      });
    }
    
    const unusedEssential = this.results.essentialComponents.filter(item => !item.used);
    if (unusedEssential.length > 0) {
      this.log('\n⚠️  Componenti essenziali da verificare:', 'yellow');
      unusedEssential.forEach(item => {
        this.log(`# Verifica se necessario: src${item.path}`, 'yellow');
      });
    }
    
    const unusedDebug = this.results.debugComponents.filter(item => !item.used);
    if (unusedDebug.length > 0) {
      this.log('\n🐛 Componenti debug non utilizzati (mantieni per troubleshooting):', 'yellow');
      unusedDebug.forEach(item => {
        this.log(`# Debug component: src${item.path}`, 'yellow');
      });
    }
    
    this.log('\n✅ Pagine Next.js e API routes sono mantenute automaticamente', 'green');
  }

  async run() {
    this.log('\n🚀 Identificazione File Veramente Inutilizzati', 'bold');
    this.log('Analisi intelligente che esclude pagine Next.js e componenti essenziali', 'blue');
    
    this.analyzeUsage();
    this.printResults();
    this.generateCleanupRecommendations();
    
    this.log('\n✅ Analisi completata!', 'green');
  }
}

// Esecuzione dell'analisi
if (require.main === module) {
  const finder = new TrulyUnusedFinder();
  finder.run().catch(error => {
    console.error('Errore fatale:', error);
    process.exit(1);
  });
}

module.exports = TrulyUnusedFinder;
