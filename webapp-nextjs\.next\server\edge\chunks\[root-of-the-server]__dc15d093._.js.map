{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport type { NextRequest } from 'next/server'\n\n// Rate limiting store (in produzione usare Redis)\nconst rateLimitStore = new Map<string, { count: number; resetTime: number }>()\n\n// Rate limiting middleware\nconst checkRateLimit = (request: NextRequest, maxRequests: number, windowMs: number): boolean => {\n  const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown'\n  const key = `${ip}-${request.nextUrl.pathname}`\n  const now = Date.now()\n\n  const record = rateLimitStore.get(key)\n\n  if (!record || now > record.resetTime) {\n    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs })\n    return true\n  }\n\n  if (record.count >= maxRequests) {\n    return false\n  }\n\n  record.count++\n  return true\n}\n\n// Security headers avanzati\nconst securityHeaders = {\n  'X-XSS-Protection': '1; mode=block',\n  'X-Frame-Options': 'DENY',\n  'X-Content-Type-Options': 'nosniff',\n  'Referrer-Policy': 'strict-origin-when-cross-origin',\n  'Permissions-Policy': 'geolocation=(), microphone=(), camera=()',\n  'Content-Security-Policy': [\n    \"default-src 'self'\",\n    \"script-src 'self' 'unsafe-inline' 'unsafe-eval'\",\n    \"style-src 'self' 'unsafe-inline'\",\n    \"img-src 'self' data: blob:\",\n    \"connect-src 'self' http://localhost:8001\",\n    \"frame-ancestors 'none'\"\n  ].join('; ')\n}\n\nexport function middleware(request: NextRequest) {\n  const response = NextResponse.next()\n\n  // Applica security headers avanzati\n  Object.entries(securityHeaders).forEach(([key, value]) => {\n    response.headers.set(key, value)\n  })\n\n  // Rate limiting per API routes\n  if (request.nextUrl.pathname.startsWith('/api/')) {\n    if (!checkRateLimit(request, 100, 60000)) {\n      return new NextResponse('Rate limit exceeded', {\n        status: 429,\n        headers: { 'Retry-After': '60' }\n      })\n    }\n  }\n\n  // Rate limiting per login\n  if (request.nextUrl.pathname === '/api/auth/login') {\n    if (!checkRateLimit(request, 5, 300000)) { // 5 tentativi per 5 minuti\n      return new NextResponse('Too many login attempts', {\n        status: 429,\n        headers: { 'Retry-After': '300' }\n      })\n    }\n  }\n\n  // Blocca User-Agent sospetti\n  const userAgent = request.headers.get('user-agent') || ''\n  const suspiciousAgents = ['sqlmap', 'nikto', 'nmap', 'burpsuite']\n\n  if (suspiciousAgents.some(agent => userAgent.toLowerCase().includes(agent))) {\n    return new NextResponse('Forbidden', { status: 403 })\n  }\n\n  // Blocca payload sospetti\n  const url = request.nextUrl.toString()\n  if (/[<>\\\"']|union|select|insert|javascript:/gi.test(url)) {\n    return new NextResponse('Bad Request', { status: 400 })\n  }\n\n  // Log accessi sensibili\n  if (request.nextUrl.pathname.startsWith('/admin')) {\n    const ip = request.ip || 'unknown'\n    console.log(`Admin access from ${ip} to ${request.nextUrl.pathname}`)\n  }\n\n  return response\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',\n  ]}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGA,kDAAkD;AAClD,MAAM,iBAAiB,IAAI;AAE3B,2BAA2B;AAC3B,MAAM,iBAAiB,CAAC,SAAsB,aAAqB;IACjE,MAAM,KAAK,QAAQ,EAAE,IAAI,QAAQ,OAAO,CAAC,GAAG,CAAC,sBAAsB;IACnE,MAAM,MAAM,GAAG,GAAG,CAAC,EAAE,QAAQ,OAAO,CAAC,QAAQ,EAAE;IAC/C,MAAM,MAAM,KAAK,GAAG;IAEpB,MAAM,SAAS,eAAe,GAAG,CAAC;IAElC,IAAI,CAAC,UAAU,MAAM,OAAO,SAAS,EAAE;QACrC,eAAe,GAAG,CAAC,KAAK;YAAE,OAAO;YAAG,WAAW,MAAM;QAAS;QAC9D,OAAO;IACT;IAEA,IAAI,OAAO,KAAK,IAAI,aAAa;QAC/B,OAAO;IACT;IAEA,OAAO,KAAK;IACZ,OAAO;AACT;AAEA,4BAA4B;AAC5B,MAAM,kBAAkB;IACtB,oBAAoB;IACpB,mBAAmB;IACnB,0BAA0B;IAC1B,mBAAmB;IACnB,sBAAsB;IACtB,2BAA2B;QACzB;QACA;QACA;QACA;QACA;QACA;KACD,CAAC,IAAI,CAAC;AACT;AAEO,SAAS,WAAW,OAAoB;IAC7C,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI;IAElC,oCAAoC;IACpC,OAAO,OAAO,CAAC,iBAAiB,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QACnD,SAAS,OAAO,CAAC,GAAG,CAAC,KAAK;IAC5B;IAEA,+BAA+B;IAC/B,IAAI,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU;QAChD,IAAI,CAAC,eAAe,SAAS,KAAK,QAAQ;YACxC,OAAO,IAAI,6LAAA,CAAA,eAAY,CAAC,uBAAuB;gBAC7C,QAAQ;gBACR,SAAS;oBAAE,eAAe;gBAAK;YACjC;QACF;IACF;IAEA,0BAA0B;IAC1B,IAAI,QAAQ,OAAO,CAAC,QAAQ,KAAK,mBAAmB;QAClD,IAAI,CAAC,eAAe,SAAS,GAAG,SAAS;YACvC,OAAO,IAAI,6LAAA,CAAA,eAAY,CAAC,2BAA2B;gBACjD,QAAQ;gBACR,SAAS;oBAAE,eAAe;gBAAM;YAClC;QACF;IACF;IAEA,6BAA6B;IAC7B,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;IACvD,MAAM,mBAAmB;QAAC;QAAU;QAAS;QAAQ;KAAY;IAEjE,IAAI,iBAAiB,IAAI,CAAC,CAAA,QAAS,UAAU,WAAW,GAAG,QAAQ,CAAC,SAAS;QAC3E,OAAO,IAAI,6LAAA,CAAA,eAAY,CAAC,aAAa;YAAE,QAAQ;QAAI;IACrD;IAEA,0BAA0B;IAC1B,MAAM,MAAM,QAAQ,OAAO,CAAC,QAAQ;IACpC,IAAI,4CAA4C,IAAI,CAAC,MAAM;QACzD,OAAO,IAAI,6LAAA,CAAA,eAAY,CAAC,eAAe;YAAE,QAAQ;QAAI;IACvD;IAEA,wBAAwB;IACxB,IAAI,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW;QACjD,MAAM,KAAK,QAAQ,EAAE,IAAI;QACzB,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,GAAG,IAAI,EAAE,QAAQ,OAAO,CAAC,QAAQ,EAAE;IACtE;IAEA,OAAO;AACT;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;;KAOC,GACD;KACD;AAAA"}}]}