'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { PrimaryButton } from '@/components/ui/animated-button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

import { usersApi } from '@/lib/api'
import { Loader2, Database, RefreshCw, Eye } from 'lucide-react'

export default function DatabaseView() {
  const [dbData, setDbData] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  // Carica tutti i dati del database
  const loadDatabaseData = async () => {
    setLoading(true)
    setError('')

    try {
      const data = await usersApi.getDatabaseData()
      setDbData(data)
    } catch (err: any) {
      setError(err.response?.data?.detail || err.message || 'Errore durante il caricamento dei dati del database')
    } finally {
      setLoading(false)

  useEffect(() => {
    loadDatabaseData()
  }, [])

  // Renderizza una tabella specifica
  const renderTable = (tableName: string, tableData: any[], title: string) => {
    if (!tableData || tableData.length === 0) {
      return (
        <div className="text-center py-4 text-slate-500 border rounded-lg">
          Nessun dato disponibile per {title}
        </div>
      )

    // Ottieni le chiavi per le colonne (dal primo elemento)
    const columns = Object.keys(tableData[0])

    return (
      <div className="border rounded-lg overflow-hidden mb-6">
        <div className="bg-slate-100 px-4 py-3 border-b">
          <h4 className="font-medium text-slate-900">{title}</h4>
          <p className="text-sm text-slate-600">Totale record: {tableData.length}</p>
        </div>
        <div className="overflow-x-auto max-h-96">
          <Table>
            <TableHeader className="sticky top-0 bg-slate-50">
              <TableRow>
                {columns.map((column) => (
                  <TableHead key={column} className="font-medium">
                    {column}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {tableData.map((row, index) => (
                <TableRow key={index}>
                  {columns.map((column) => (
                    <TableCell key={column} className="font-mono text-sm">
                      {row[column] !== null && row[column] !== undefined
                        ? String(row[column])
                        : <span className="text-slate-400">NULL</span>

                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    )

  // Definisce le tabelle disponibili
  const tables = [
    { key: 'users', title: 'Utenti', description: 'Tutti gli utenti del sistema' },
    { key: 'cantieri', title: 'Cantieri', description: 'Tutti i cantieri/progetti' },
    { key: 'cavi', title: 'Cavi', description: 'Tutti i cavi installati' },
    { key: 'parco_cavi', title: 'Bobine', description: 'Tutte le bobine del parco cavi' },
    { key: 'strumenti_certificati', title: 'Strumenti', description: 'Strumenti certificati' },
    { key: 'certificazioni_cavi', title: 'Certificazioni', description: 'Certificazioni dei cavi' }
  ]

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Visualizzazione Database Raw
          </CardTitle>
          <PrimaryButton
            size="sm"
            onClick={loadDatabaseData}
            loading={loading}
            icon={<RefreshCw className="h-4 w-4" />}
          >
            Aggiorna
          </PrimaryButton>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <Eye className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900">Visualizzazione Raw del Database</h4>
              <p className="text-sm text-blue-700 mt-1">
                Questa sezione mostra i dati grezzi delle tabelle del database. 
                Utile per debugging e analisi dei dati.
              </p>
            </div>
          </div>
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin mr-3" />
            <span className="text-lg">Caricamento dati database...</span>
          </div>
        ) : error ? (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <p className="text-red-600 font-medium">Errore durante il caricamento:</p>
            <p className="text-red-600">{error}</p>
          </div>
        ) : !dbData ? (
          <div className="text-center py-12 text-slate-500">
            Nessun dato disponibile
          </div>
        ) : (
          <div className="space-y-8">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p className="text-sm text-blue-700">
                Visualizzazione completa di tutte le tabelle del database.
                I dati sono mostrati in formato raw per debugging e analisi.
              </p>
            </div>

            {tables.map((table) => (
              dbData[table.key] && (
                <div key={table.key}>
                  <div className="mb-4">
                    <h3 className="text-xl font-semibold text-slate-900">{table.title}</h3>
                    <p className="text-sm text-slate-600">{table.description}</p>
                  </div>
                  {renderTable(table.key, dbData[table.key], table.title)}
                </div>
              )
            ))}

            {/* Mostra un riepilogo */}
            <div className="bg-slate-50 border border-slate-200 rounded-lg p-4">
              <h4 className="font-medium text-slate-900 mb-2">Riepilogo Database</h4>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                {tables.map((table) => (
                  <div key={table.key} className="flex justify-between">
                    <span className="text-slate-600">{table.title}:</span>
                    <span className="font-medium">
                      {dbData[table.key] ? dbData[table.key].length : 0} record
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
